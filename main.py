import smtplib
import imaplib
import email
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import time
import re
from datetime import datetime

# Import cấu hình
from config import (
    SMTP_SETTINGS, IMAP_SETTINGS, GEMINI_API_KEY,
    CHATBOT_SETTINGS, COMPANY_INFO,
    KEYWORD_RESPONSES, DEFAULT_RESPONSE, validate_config
)

# Import modules
from gemini_ai import GeminiA<PERSON>
from intent_detector import IntentDetector
from context_manager import get_context_manager
from response_generator import ResponseGenerator


class EmailChatbot:
    def __init__(self):
        self.smtp_server = SMTP_SETTINGS["SMTP_SERVER"]
        self.smtp_port = SMTP_SETTINGS["SMTP_PORT"]
        self.sender_email = SMTP_SETTINGS["SENDER_MAIL"]
        self.password = SMTP_SETTINGS["PASSWORD"]

        self.imap_server = IMAP_SETTINGS["IMAP_SERVER"]
        self.imap_port = IMAP_SETTINGS["IMAP_PORT"]

        # <PERSON><PERSON><PERSON> trữ các email đã xử lý để tránh trùng lặp
        self.processed_emails = set()

        # Khởi tạo AI và Intent system
        self.gemini_ai = GeminiAI()
        self.intent_detector = IntentDetector(
            self.gemini_ai)  # Truyền AI vào detector
        self.context_manager = get_context_manager()
        self.response_generator = ResponseGenerator(
            self.gemini_ai, self.context_manager)

        # Cờ để bật/tắt AI (từ config)
        self.use_ai = CHATBOT_SETTINGS['USE_AI']

    def toggle_ai(self, enable=None):
        """Bật/tắt chế độ AI"""
        if enable is None:
            self.use_ai = not self.use_ai
        else:
            self.use_ai = enable

        status = "BẬT" if self.use_ai else "TẮT"
        print(f"🤖 Chế độ AI: {status}")
        return self.use_ai

    def send_email(self, to_email, subject, message):
        """Gửi email phản hồi"""
        try:
            # Tạo message object
            msg = MIMEMultipart()
            msg['From'] = self.sender_email
            msg['To'] = to_email
            msg['Subject'] = subject

            # Thêm nội dung email
            msg.attach(MIMEText(message, 'plain', 'utf-8'))

            # Kết nối SMTP server
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            server.starttls()  # Bật mã hóa TLS
            server.login(self.sender_email, self.password)

            # Gửi email
            text = msg.as_string()
            server.sendmail(self.sender_email, to_email, text)
            server.quit()

            print(f"✅ Đã gửi email thành công đến {to_email}")
            return True

        except Exception as e:
            print(f"❌ Lỗi khi gửi email: {str(e)}")
            return False

    def read_emails(self):
        """Đọc email từ hộp thư đến"""
        try:
            # Kết nối IMAP server
            mail = imaplib.IMAP4_SSL(self.imap_server, self.imap_port)
            mail.login(self.sender_email, self.password)
            mail.select('inbox')

            # Tìm email chưa đọc
            status, messages = mail.search(None, 'UNSEEN')
            email_ids = messages[0].split()

            new_emails = []

            for email_id in email_ids:
                # Lấy email
                status, msg_data = mail.fetch(email_id, '(RFC822)')

                for response_part in msg_data:
                    if isinstance(response_part, tuple):
                        # Parse email
                        msg = email.message_from_bytes(response_part[1])

                        # Lấy thông tin email
                        subject = msg['subject']
                        from_email = msg['from']

                        # Lấy nội dung email
                        body = self.get_email_body(msg)

                        email_info = {
                            'id': email_id.decode(),
                            'from': from_email,
                            'subject': subject,
                            'body': body
                        }

                        new_emails.append(email_info)

            mail.close()
            mail.logout()

            return new_emails

        except Exception as e:
            print(f"❌ Lỗi khi đọc email: {str(e)}")
            return []

    def get_email_body(self, msg):
        """Lấy nội dung email"""
        body = ""

        if msg.is_multipart():
            for part in msg.walk():
                content_type = part.get_content_type()
                content_disposition = str(part.get("Content-Disposition"))

                if content_type == "text/plain" and "attachment" not in content_disposition:
                    try:
                        body = part.get_payload(decode=True).decode('utf-8')
                    except:
                        try:
                            body = part.get_payload(
                                decode=True).decode('latin-1')
                        except:
                            body = str(part.get_payload())
                    break
        else:
            try:
                body = msg.get_payload(decode=True).decode('utf-8')
            except:
                try:
                    body = msg.get_payload(decode=True).decode('latin-1')
                except:
                    body = str(msg.get_payload())

        return body.strip()

    def generate_response(self, email_content, sender_email, subject=""):
        """Tạo phản hồi tự động dựa trên nội dung email với Intent Detection"""

        # Sử dụng email làm user_id (có thể cải thiện sau)
        user_id = sender_email

        # Phát hiện intent
        has_context = self.context_manager.has_context(user_id)
        intent_result = self.intent_detector.detect_intent(
            email_content, has_context)

        print(
            f"🎯 Intent detected: {intent_result.intent.value} (confidence: {intent_result.confidence:.2f})")

        # Tạo phản hồi dựa trên intent
        if self.use_ai:
            try:
                response = self.response_generator.generate_response(
                    user_id, email_content, intent_result
                )
                print("🤖 Sử dụng phản hồi từ Intent System + AI")

                # Lưu vào context
                self.context_manager.add_conversation_turn(
                    user_id, email_content, intent_result, response
                )

                return response
            except Exception as e:
                print(
                    f"⚠️ Intent System lỗi, chuyển sang phản hồi cơ bản: {str(e)}")

        # Phản hồi cơ bản dựa trên từ khóa (fallback)
        print("📝 Sử dụng phản hồi cơ bản")
        return self._generate_keyword_response(email_content, sender_email)

    def _generate_keyword_response(self, email_content, sender_email):
        """Tạo phản hồi dựa trên từ khóa (phương pháp cũ)"""
        # Chuyển nội dung về chữ thường để dễ xử lý
        content_lower = email_content.lower()

        # Tìm phản hồi phù hợp từ config
        for keywords, response_data in KEYWORD_RESPONSES.items():
            if re.search(keywords, content_lower):
                response = response_data.get('vi', response_data)
                # Thay thế timestamp nếu có
                if '{timestamp}' in response:
                    response = response.replace(
                        '{timestamp}', datetime.now().strftime('%Y%m%d%H%M%S'))
                return response

        # Phản hồi mặc định từ config
        return DEFAULT_RESPONSE.get('vi', DEFAULT_RESPONSE)

    def process_emails(self):
        """Xử lý email và gửi phản hồi tự động"""
        print("🔍 Đang kiểm tra email mới...")

        # Đọc email mới
        new_emails = self.read_emails()

        if not new_emails:
            print("📭 Không có email mới")
            return

        print(f"📧 Tìm thấy {len(new_emails)} email mới")

        for email_info in new_emails:
            email_id = email_info['id']

            # Kiểm tra email đã xử lý chưa
            if email_id in self.processed_emails:
                continue

            from_email = email_info['from']
            subject = email_info['subject'] or "No Subject"
            body = email_info['body']

            print(f"\n📨 Xử lý email từ: {from_email}")
            print(f"📋 Tiêu đề: {subject}")

            # Tạo phản hồi
            response = self.generate_response(body, from_email, subject)

            # Tạo tiêu đề phản hồi
            reply_subject = f"Re: {subject}" if not subject.startswith(
                "Re:") else subject

            # Gửi phản hồi
            if self.send_email(from_email, reply_subject, response):
                self.processed_emails.add(email_id)
                print(f"✅ Đã phản hồi email từ {from_email}")
            else:
                print(f"❌ Không thể phản hồi email từ {from_email}")

    def run_chatbot(self, check_interval=60):
        """Chạy chatbot liên tục"""
        print("🤖 Email Chatbot đã khởi động!")
        print(f"📧 Email: {self.sender_email}")
        print(f"⏰ Kiểm tra email mỗi {check_interval} giây")
        print("=" * 50)

        try:
            while True:
                self.process_emails()
                print(
                    f"\n⏳ Chờ {check_interval} giây trước khi kiểm tra lại...")
                time.sleep(check_interval)

        except KeyboardInterrupt:
            print("\n🛑 Chatbot đã dừng!")
        except Exception as e:
            print(f"\n❌ Lỗi: {str(e)}")


def main():
    """Hàm chính"""
    print("🚀 Khởi động Email Chatbot với AI...")
    print("=" * 50)

    # Kiểm tra cấu hình
    if not validate_config():
        print("\n❌ Cấu hình không đầy đủ!")
        print("💡 Hướng dẫn:")
        print("1. Copy file .env.example thành .env")
        print("2. Cập nhật các giá trị trong file .env")
        print("3. Chạy lại chatbot")
        return

    chatbot = EmailChatbot()

    # Hiển thị thông tin cấu hình
    print(f"📧 Email: {chatbot.sender_email}")
    print(f"🤖 AI Mode: {'BẬT' if chatbot.use_ai else 'TẮT'}")
    print(
        f"🔑 Gemini API: {'Đã cấu hình' if GEMINI_API_KEY else 'Chưa cấu hình'}")
    print("=" * 50)

    # Test gửi email
    print("🧪 Test gửi email...")
    test_result = chatbot.send_email(
        CHATBOT_SETTINGS['TEST_EMAIL'],
        "Test Email Chatbot với AI",
        "Đây là email test từ chatbot với tích hợp Gemini AI!"
    )

    if test_result:
        print("✅ Test gửi email thành công!")
    else:
        print("❌ Test gửi email thất bại!")
        return

    # Test AI (nếu được bật)
    if chatbot.use_ai:
        print("\n🤖 Test Gemini AI...")
        try:
            test_response = chatbot.gemini_ai.generate_response(
                "Xin chào, tôi muốn biết thêm về dịch vụ của công ty",
                CHATBOT_SETTINGS['TEST_EMAIL'],
                "Hỏi về dịch vụ"
            )
            print("✅ Gemini AI hoạt động bình thường")
            print(f"📝 Mẫu phản hồi AI:\n{test_response[:100]}...")
        except Exception as e:
            print(f"⚠️ Gemini AI có vấn đề: {str(e)}")
            print("📝 Sẽ sử dụng phản hồi cơ bản")

    print("\n" + "=" * 50)
    print("💡 Gợi ý: Nhấn Ctrl+C để dừng chatbot")
    print("=" * 50)

    # Chạy chatbot
    chatbot.run_chatbot(check_interval=CHATBOT_SETTINGS['CHECK_INTERVAL'])


if __name__ == "__main__":
    main()
