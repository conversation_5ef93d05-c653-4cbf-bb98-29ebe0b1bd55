#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for the new generate_job_it_trending_response function
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from new_response_generator import NewResponseGenerator
from gemini_ai import GeminiAI
import json

def test_trending_response():
    """Test the new trending response function"""
    
    print("🚀 Testing IT Trending Response Function")
    print("=" * 50)
    
    try:
        # Initialize components
        print("📝 Initializing AI components...")
        gemini_ai = GeminiAI()
        response_gen = NewResponseGenerator(gemini_ai)
        
        # Test questions about IT trends
        test_questions = [
            "Thị trường IT Việt Nam 2024-2025 như thế nào?",
            "Công nghệ nào đang hot nhất hiện tại?",
            "Mức lương AI Engineer bao nhiêu?",
            "Python Developer có cơ hội như thế nào?",
            "Freelance IT có thu nhập cao không?"
        ]
        
        print(f"✅ Components initialized successfully!")
        print(f"🎯 Testing {len(test_questions)} trending questions...\n")
        
        for i, question in enumerate(test_questions, 1):
            print(f"📊 Test {i}: {question}")
            print("-" * 40)
            
            try:
                # Generate response
                response = response_gen.generate_job_it_trending_response(
                    user_id="test_user",
                    user_input=question,
                    conversation_history=[]
                )
                
                if response:
                    print(f"✅ Response generated successfully!")
                    print(f"📝 Message length: {len(response.get('message', ''))}")
                    print(f"🎭 Tone: {response.get('tone', 'N/A')}")
                    print(f"😊 Emotion: {response.get('emotion', 'N/A')}")
                    print(f"💡 Suggestions: {len(response.get('suggestion_answers', []))}")
                    
                    # Show market data if available
                    market_data = response.get('market_data', {})
                    if market_data:
                        print(f"📈 Market Data:")
                        print(f"   - Hot Technologies: {len(market_data.get('hot_technologies', []))}")
                        print(f"   - Salary Ranges: {len(market_data.get('salary_ranges', []))}")
                        print(f"   - Trending Jobs: {len(market_data.get('trending_jobs', []))}")
                    
                    # Show first 200 chars of response
                    message = response.get('message', '')
                    preview = message[:200] + "..." if len(message) > 200 else message
                    print(f"📄 Response Preview: {preview}")
                    
                else:
                    print("❌ No response generated")
                    
            except Exception as e:
                print(f"❌ Error: {str(e)}")
            
            print("\n")
        
        # Test market insights summary
        print("📊 Testing Market Insights Summary...")
        print("-" * 40)
        
        try:
            insights = response_gen.get_market_insights_summary()
            print("✅ Market insights retrieved successfully!")
            print(f"📈 Market Status: {insights.get('market_status', 'N/A')}")
            print(f"🔥 Hot Technologies: {len(insights.get('hot_technologies', {}))}")
            print(f"💼 Top Positions: {len(insights.get('top_positions', []))}")
            print(f"📊 Key Trends: {len(insights.get('key_trends', []))}")
            
            # Show some details
            hot_tech = insights.get('hot_technologies', {})
            if hot_tech:
                print("\n🚀 Hot Technologies:")
                for tech, data in hot_tech.items():
                    print(f"   - {tech.upper()}: {data.get('salary', 'N/A')} (Demand: {data.get('demand', 'N/A')})")
            
        except Exception as e:
            print(f"❌ Error getting market insights: {str(e)}")
        
        print("\n" + "=" * 50)
        print("🎉 Testing completed!")
        
    except Exception as e:
        print(f"❌ Failed to initialize components: {str(e)}")
        print("💡 Make sure GEMINI_API_KEY is configured in your .env file")

def test_sample_data_import():
    """Test if sample data can be imported correctly"""
    print("\n🔍 Testing Sample Data Import...")
    print("-" * 30)
    
    try:
        from sample_data import (
            IT_MARKET_TRENDS_2024_2025, HOT_TECH_STACK_2024, 
            FREELANCE_MARKET_2024, IT_FORECAST_2025,
            JOB_MARKET_INSIGHTS_2024, SALARY_RANGES
        )
        
        print("✅ All market data imported successfully!")
        print(f"📊 Market trends: {len(IT_MARKET_TRENDS_2024_2025)} sections")
        print(f"🔥 Hot tech stack: {len(HOT_TECH_STACK_2024)} categories")
        print(f"💼 Freelance market: {len(FREELANCE_MARKET_2024)} sections")
        print(f"🔮 2025 forecast: {len(IT_FORECAST_2025)} sections")
        print(f"💰 Salary ranges: {len(SALARY_RANGES)} positions")
        print(f"📈 Job insights: {len(JOB_MARKET_INSIGHTS_2024)} sections")
        
        # Show some sample data
        print(f"\n📄 Sample AI/ML salary: {SALARY_RANGES.get('ai_ml_engineer', {}).get('range', 'N/A')}")
        print(f"🐍 Sample Python salary: {SALARY_RANGES.get('python_developer', {}).get('range', 'N/A')}")
        
    except ImportError as e:
        print(f"❌ Import error: {str(e)}")
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    print("🧪 FOIS Chatbot - IT Trending Response Test")
    print("=" * 60)
    
    # Test data import first
    test_sample_data_import()
    
    # Test the trending response function
    test_trending_response()
    
    print("\n💡 To test via web interface:")
    print("   1. Run: python web_app.py")
    print("   2. Visit: http://localhost:5001")
    print("   3. Ask: 'Thị trường IT Việt Nam 2024-2025 như thế nào?'")
    print("   4. Or use API: POST /api/trending-jobs")
