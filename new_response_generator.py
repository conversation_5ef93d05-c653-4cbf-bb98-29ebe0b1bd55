#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
New Response Generator for 3-Type Customer Intent System
"""

from typing import Dict, List

from gemini_ai import Gemini<PERSON><PERSON>
from config import COMPANY_INFO

from google import genai
from google.genai import types
from bot_personality import BOT_PERSONALITY
from sample_data import *


class NewResponseGenerator:
    """Response generator cho hệ thống intent mới"""

    def __init__(self, gemini_ai: GeminiAI):
        self.gemini_ai = gemini_ai

        self.client = genai.Client(
            api_key="AIzaSyCoK8VjREZ11HU3QllxwZUbFSO_WbMwbpE")
        self.gemini_model = "gemini-2.5-flash"
        self.prompt_config = self.build_system_prompt_from_config(
            BOT_PERSONALITY)

    def build_system_prompt_from_config(self, config) -> str:
        traits = config.get("traits", {})
        style = config.get("communication_style", {})
        addressing = config.get("addressing", {})

        return f"""
    You are a virtual assistant named {config['name']}, working as a {config['role']} at {config['company']}.

Your personality traits include:
- Friendliness: {traits.get('friendliness', 0)}/10
- Professionalism: {traits.get('professionalism', 0)}/10
- Enthusiasm: {traits.get('enthusiasm', 0)}/10
- Helpfulness: {traits.get('helpfulness', 0)}/10
- Patience: {traits.get('patience', 0)}/10
- Humor: {traits.get('humor', 0)}/10
- Empathy: {traits.get('empathy', 0)}/10
- Energy: {traits.get('energy', 0)}/10

Your communication style is:
- Tone: {style.get('tone', 'friendly')}
- Formality: {style.get('formality', 'semi_formal')}
- Emoji usage: {style.get('emoji_usage', 'moderate')}
- Language style: {style.get('language_style', 'conversational')}
- Preferred response length: {style.get('response_length', 'medium')}

When referring to yourself, use: {", ".join(addressing.get("self_pronouns", []))}
When referring to the user, use: {", ".join(addressing.get("user_pronouns", []))}

Always greet users with: "{addressing.get("preferred_greeting", 'Hello')}"
Always end conversations with: "{addressing.get("preferred_farewell", 'Do you have anything else?')}"

You are a career assistant on the Asiantech.link platform. Your responsibilities are:

- Help users find jobs, improve their CVs, and answer career-related questions.
- Focus on job search, application support, resume guidance, and recruitment-related queries.

Language behavior:
- Always detect the user's input language.
- If the user writes in Vietnamese, respond entirely in Vietnamese (no mixing).
- If the user writes in English, respond entirely in English (no mixing).
- Never mix languages in a single sentence or paragraph.
- Maintain consistent tone, tone markers (e.g., emojis), and professional structure in the selected language.

Off-topic policy:
- If the user's message is not related to jobs or careers (e.g., asking about Flutter, programming tutorials, unrelated technical help, or casual conversation), respond politely but briefly, stating that it's outside your support scope.
- Do NOT generate long answers for unrelated topics. Keep responses short and friendly (1–2 sentences max).
- Maintain a consistent tone based on your personality config (friendly, professional, helpful).
        """.strip()

    def generate_response(self, user_id: str, user_input: str, conversation_history: List[str] = None) -> Dict:
        generate_content_config = types.GenerateContentConfig(
            response_mime_type="application/json",
            temperature=0.5,
            response_schema=types.Schema(
                type=types.Type.OBJECT,
                properties={
                    "thinking_message": types.Schema(
                        type=types.Type.STRING,
                        description="A short message the bot shows to the user while processing a request, e.g., searching for jobs. This message should match the tone and context, and should feel natural to the user."
                    ),
                    "message": types.Schema(type=types.Type.STRING, description="Message to response to user"),
                    "response_type": types.Schema(type=types.Type.STRING, description="Type of response (e.g., 'text', 'image', 'video')"),
                    "tone": types.Schema(type=types.Type.STRING, description="Tone of the response (e.g., 'friendly', 'formal')"),
                    "emotion": types.Schema(type=types.Type.STRING, description="Emotion to display (e.g., 'happy', 'sad')"),
                    "user_intent": types.Schema(type=types.Type.STRING, description="Type of user intent",
                                                enum=[
                                                    "greeting",
                                                    "farewell",
                                                    "ask_company_info",
                                                    "ask_bot_info",
                                                    "search_jobs",
                                                    "filter_jobs",
                                                    "apply_job",
                                                    "cancel_application",
                                                    "upload_resume",
                                                    "update_resume",
                                                    "get_application_status",
                                                    "job_recommendation",
                                                    "follow_up_job",
                                                    "location_query",
                                                    "tech_stack_query",
                                                    "salary_query",
                                                    "interview_process",
                                                    "cv_feedback",
                                                    "smalltalk",
                                                    "off_topic",
                                                    "thank_you",
                                                    "complaint",
                                                    "bug_report",
                                                    "request_human_support",
                                                    "job_it_trending"
                                                ],
                                                ),
                    "needs_callback": types.Schema(
                        type=types.Type.BOOLEAN,
                        description="Whether this user_intent is search_jobs, filter_jobs, or salary_query, ask_company_info, cv_feedback,upload_resume,update_resume, job_it_trending"
                    ),
                    "suggestion_answers": types.Schema(type=types.Type.ARRAY, description="List of suggestion answers",
                                                       items=types.Schema(
                                                           type=types.Type.STRING)
                                                       ),
                    "contextual_followup": types.Schema(type=types.Type.OBJECT, description="Contextual follow-up question",
                                                        properties={
                                                            "topic": types.Schema(type=types.Type.STRING, description="Topic of the follow-up question"),
                                                            "prompt": types.Schema(type=types.Type.STRING, description="Prompt for the follow-up question"),
                                                        })

                },
                required=["message", "response_type", "tone", "emotion",
                          "user_intent", "suggestion_answers", "contextual_followup", "thinking_message", "needs_callback"]
            ),
        )

        contents = []

        # Add bot personality prompt
        intro_prompt = types.Part.from_text(
            text=f"{self.prompt_config}\n\n---\nBelow is the conversation history and the latest message:"
        )
        contents.append(types.Content(role="user", parts=[intro_prompt]))

        # Add chat history
        if conversation_history:
            for msg in conversation_history:
                contents.append(types.Content(
                    role="user", parts=[types.Part.from_text(text=msg)]))

        # Add current user message
        contents.append(types.Content(role="user", parts=[
                        types.Part.from_text(text=user_input)]))

        # Generate
        response = self.client.models.generate_content(
            model=self.gemini_model,
            contents=contents,
            config=generate_content_config
        )

        dataJson = response.model_dump().get("parsed")

        # Extract token usage if available
        if hasattr(response, 'usage_metadata') and response.usage_metadata:
            usage = response.usage_metadata
            dataJson['token_usage'] = {
                'input_tokens': getattr(usage, 'prompt_token_count', 0),
                'output_tokens': getattr(usage, 'candidates_token_count', 0),
                'total_tokens': getattr(usage, 'total_token_count', 0)
            }

        return dataJson

    def generate_thinking_message(self, user_id: str, user_input: str, conversation_history: List[str] = None) -> Dict:
        """Generate only thinking message for immediate callback"""
        generate_content_config = types.GenerateContentConfig(
            response_mime_type="application/json",
            temperature=0.5,
            response_schema=types.Schema(
                type=types.Type.OBJECT,
                properties={
                    "thinking_message": types.Schema(
                        type=types.Type.STRING,
                        description="A short message the bot shows to the user while processing a request, e.g., searching for jobs. This message should match the tone and context, and should feel natural to the user."
                    ),
                    "user_intent": types.Schema(type=types.Type.STRING, description="Type of user intent",
                                                enum=[
                                                    "greeting",
                                                    "farewell",
                                                    "ask_company_info",
                                                    "ask_bot_info",
                                                    "search_jobs",
                                                    "filter_jobs",
                                                    "apply_job",
                                                    "cancel_application",
                                                    "upload_resume",
                                                    "update_resume",
                                                    "get_application_status",
                                                    "job_recommendation",
                                                    "follow_up_job",
                                                    "location_query",
                                                    "tech_stack_query",
                                                    "salary_query",
                                                    "interview_process",
                                                    "cv_feedback",
                                                    "smalltalk",
                                                    "off_topic",
                                                    "thank_you",
                                                    "complaint",
                                                    "bug_report",
                                                    "request_human_support"
                                                ],
                                                ),
                    "needs_callback": types.Schema(
                        type=types.Type.BOOLEAN,
                        description="Whether this intent requires a callback for additional processing (e.g., job search, filtering)"
                    )
                },
                required=["thinking_message", "user_intent", "needs_callback"]
            ),
        )

        contents = []

        # Add bot personality prompt for thinking message
        thinking_prompt = types.Part.from_text(
            text=f"{self.prompt_config}\n\n---\nGenerate a short thinking message for this user input. If the intent is search_jobs or filter_jobs, set needs_callback to true and provide an appropriate thinking message like 'Đang tìm kiếm việc làm phù hợp cho bạn...' or 'Đang lọc danh sách công việc...'. For other intents, set needs_callback to false.\n\nUser input: {user_input}"
        )
        contents.append(types.Content(role="user", parts=[thinking_prompt]))

        # Generate
        response = self.client.models.generate_content(
            model=self.gemini_model,
            contents=contents,
            config=generate_content_config
        )

        dataJson = response.model_dump().get("parsed")

        # Extract token usage if available
        if hasattr(response, 'usage_metadata') and response.usage_metadata:
            usage = response.usage_metadata
            dataJson['token_usage'] = {
                'input_tokens': getattr(usage, 'prompt_token_count', 0),
                'output_tokens': getattr(usage, 'candidates_token_count', 0),
                'total_tokens': getattr(usage, 'total_token_count', 0)
            }

        return dataJson

    def generate_thinking_message_response(self, user_id: str, user_input: str, conversation_history: List[str] = None, user_intent: str = None) -> Dict:
        """Generate response after thinking message"""
        generate_content_config = types.GenerateContentConfig(
            response_mime_type="application/json",
            temperature=0.5,
            response_schema=types.Schema(
                type=types.Type.OBJECT,
                properties={
                    "message": types.Schema(type=types.Type.STRING, description="Company info response"),
                    "response_type": types.Schema(type=types.Type.STRING, description="Type of response"),
                    "tone": types.Schema(type=types.Type.STRING, description="Tone of the response"),
                    "emotion": types.Schema(type=types.Type.STRING, description="Emotion to display"),
                    "suggestion_answers": types.Schema(type=types.Type.ARRAY, description="List of suggestion answers",
                                                       items=types.Schema(type=types.Type.STRING)),
                    "contextual_followup": types.Schema(type=types.Type.OBJECT, description="Contextual follow-up question",
                                                        properties={
                                                            "topic": types.Schema(type=types.Type.STRING, description="Topic of the follow-up question"),
                                                            "prompt": types.Schema(type=types.Type.STRING, description="Prompt for the follow-up question"),
                                                        })
                },
                required=["message", "response_type", "tone", "emotion",
                          "suggestion_answers", "contextual_followup"]
            ),
        )
        prompt = self.generate_prompt_based_on_user_intent(
            user_intent, user_input)

        contents = []

        # Add specialized prompt for company info
        company_info_prompt = types.Part.from_text(
            text=prompt)
        contents.append(types.Content(
            role="user", parts=[company_info_prompt]))

        if conversation_history:
            for msg in conversation_history[-10:]:
                contents.append(types.Content(
                    role="user", parts=[types.Part.from_text(text=msg)]))

        # Generate
        response = self.client.models.generate_content(
            model=self.gemini_model,
            contents=contents,
            config=generate_content_config
        )

        dataJson = response.model_dump().get("parsed")

        # Extract token usage if available
        if hasattr(response, 'usage_metadata') and response.usage_metadata:
            usage = response.usage_metadata
            dataJson['token_usage'] = {
                'input_tokens': getattr(usage, 'prompt_token_count', 0),
                'output_tokens': getattr(usage, 'candidates_token_count', 0),
                'total_tokens': getattr(usage, 'total_token_count', 0)
            }

        return dataJson

    def generate_prompt_based_on_user_intent(self, user_intent, user_input):
        if user_intent == "ask_company_info":
            return f"""
{self.prompt_config}
---
The user is asking for company info. Here is company details:
{COMPANY_DETAILS}
---
User input: {user_input}
"""
        elif user_intent in ['search_jobs', 'filter_jobs', 'salary_query']:
            return f"""
{self.prompt_config}

---
The user is looking for jobs. Provide a comprehensive job search response with:
1. Available job positions at FOIS GROUP
2. Salary ranges
3. Requirements
4. Next steps (CV upload, application process)
5. Relevant suggestions for follow-up questions

Make the response detailed, helpful, and encouraging. Include specific job titles, salary ranges, and actionable advice.
Job available:
{SAMPLE_JOBS}

Salary ranges:
{SALARY_RANGES}

---
User input: {user_input}
"""
        elif user_intent in ["cv_feedback", "upload_resume", "update_resume"]:
            return f"""
{self.prompt_config}

🎯 Your core responsibilities include:
1. Reviewing user CVs and giving feedback.
2. Suggesting improvements to CVs for better job matching.
       
Here is CV and user input:
{user_input}
"""
        elif user_intent == "job_it_trending":
            # Prepare market data context for AI
            market_context = f"""
    📊 **VIETNAM IT MARKET DATA 2024-2025** (From ITviec, TopDev, Navigos Reports):

    🔥 **HOT TECHNOLOGIES & SALARY RANGES:**
    • AI/ML: {HOT_TECH_STACK_2024['ai_ml']['salary_range']} - {HOT_TECH_STACK_2024['ai_ml']['note']}
    • Python: {HOT_TECH_STACK_2024['backend']['salary_range']} - {HOT_TECH_STACK_2024['backend']['note']}
    • Cloud/DevOps: {HOT_TECH_STACK_2024['cloud_devops']['salary_range']} - {HOT_TECH_STACK_2024['cloud_devops']['note']}
    • Blockchain: {HOT_TECH_STACK_2024['blockchain']['salary_range']} - {HOT_TECH_STACK_2024['blockchain']['note']}

    💰 **TOP PAYING POSITIONS:**
    • AI/ML Engineer: {SALARY_RANGES['ai_ml_engineer']['range']} - {SALARY_RANGES['ai_ml_engineer']['market_trend']}
    • Python Developer: {SALARY_RANGES['python_developer']['range']} - {SALARY_RANGES['python_developer']['market_trend']}
    • Blockchain Engineer: {SALARY_RANGES['blockchain_engineer']['range']} - {SALARY_RANGES['blockchain_engineer']['market_trend']}

    🚀 **MARKET TRENDS:**
    • Status: {IT_MARKET_TRENDS_2024_2025['overview']['status']}
    • Key Drivers: {', '.join(IT_MARKET_TRENDS_2024_2025['overview']['key_drivers'])}
    • Challenges: {', '.join(IT_MARKET_TRENDS_2024_2025['overview']['challenges'])}

    💼 **FREELANCE MARKET:**
    • AI/ML Projects: {FREELANCE_MARKET_2024['top_earning_categories']['ai_ml_development']['avg_monthly']} (highest)
    • Web Development: {FREELANCE_MARKET_2024['top_earning_categories']['web_development']['avg_monthly']}
    • Mobile Development: {FREELANCE_MARKET_2024['top_earning_categories']['mobile_development']['avg_monthly']}

    🎯 **MOST DEMANDED POSITIONS 2024:**
    {chr(10).join([f"• {pos['title']}: {pos['avg_salary']} - {pos['market_note']}" for pos in JOB_MARKET_INSIGHTS_2024['most_demanded_positions'][:3]])}

    ⚠️ **HIRING CHALLENGES:**
    {chr(10).join([f"• {challenge}" for challenge in JOB_MARKET_INSIGHTS_2024['hiring_challenges'][:3]])}

    🔮 **2025 FORECAST:**
    • Growth Sectors: {', '.join(IT_FORECAST_2025['growth_sectors'])}
    • Market Outlook: {IT_FORECAST_2025['market_outlook']}

    🔥 **HOT JOB OPPORTUNITIES AT FOIS:**
    {chr(10).join([f"• {job['title']} {job.get('hot_tag', '')}: {job['salary_range']} - {job['location']}" for job in UPDATED_SAMPLE_JOBS_2024[:2]])}
    """
            return f"""
{self.prompt_config}

🎯 **SPECIALIZED ROLE: IT Market Analyst & Career Advisor**

You are now an expert IT market analyst with access to the latest Vietnam IT market data from 2024-2025 reports (ITviec, TopDev, Navigos). Your mission is to provide accurate, data-driven insights about:

1. 🔥 Hot technology trends and salary ranges
2. 📈 Market demand and growth sectors
3. 💰 Salary insights and career opportunities
4. 🚀 Future predictions and recommendations

**CURRENT MARKET DATA:**
{market_context}

**USER QUESTION:** {user_input}

**INSTRUCTIONS:**
- Use REAL DATA from the market reports above
- Provide specific salary ranges with VNĐ amounts
- Mention trending technologies and their demand levels
- Include actionable career advice
- Be enthusiastic about opportunities but realistic about challenges
- Use emojis and formatting to make data engaging
- Always cite that data comes from "latest 2024-2025 market reports"

**RESPONSE STYLE:**
- Professional yet friendly and encouraging
- Data-driven with specific numbers
- Include both opportunities and realistic expectations
- Suggest next steps for career development
"""

    def get_market_insights_summary(self) -> Dict:
        """Get a quick summary of current IT market insights"""
        from sample_data import (
            IT_MARKET_TRENDS_2024_2025, HOT_TECH_STACK_2024,
            JOB_MARKET_INSIGHTS_2024
        )

        return {
            "market_status": IT_MARKET_TRENDS_2024_2025['overview']['status'],
            "hot_technologies": {
                "ai_ml": {
                    "salary": HOT_TECH_STACK_2024['ai_ml']['salary_range'],
                    "demand": HOT_TECH_STACK_2024['ai_ml']['demand']
                },
                "python": {
                    "salary": HOT_TECH_STACK_2024['backend']['salary_range'],
                    "demand": HOT_TECH_STACK_2024['backend']['demand']
                },
                "cloud": {
                    "salary": HOT_TECH_STACK_2024['cloud_devops']['salary_range'],
                    "demand": HOT_TECH_STACK_2024['cloud_devops']['demand']
                }
            },
            "top_positions": [pos["title"] for pos in JOB_MARKET_INSIGHTS_2024["most_demanded_positions"][:3]],
            "salary_growth": "15-25% khi chuyển việc",
            "key_trends": [
                "AI/ML dẫn đầu về lương và nhu cầu",
                "Python là ngôn ngữ hot nhất",
                "Remote work trở thành chuẩn mực",
                "Thiếu hụt nhân lực có kinh nghiệm"
            ]
        }
