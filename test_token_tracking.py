#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for token tracking functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from new_response_generator import NewResponseGenerator
from gemini_ai import GeminiAI
import j<PERSON>

def test_token_tracking():
    """Test if token tracking is working"""
    
    print("🧪 Testing Token Tracking Functionality")
    print("=" * 50)
    
    try:
        # Initialize components
        print("📝 Initializing AI components...")
        gemini_ai = GeminiAI()
        response_gen = NewResponseGenerator(gemini_ai)
        
        print("✅ Components initialized successfully!")
        
        # Test simple message
        test_message = "Hello, how are you?"
        print(f"\n🔍 Testing with message: '{test_message}'")
        
        # Generate response
        response = response_gen.generate_response(
            user_id="test_user",
            user_input=test_message,
            conversation_history=[]
        )
        
        print("\n📊 Response Analysis:")
        print(f"✅ Response generated: {bool(response)}")
        print(f"📝 Message length: {len(response.get('message', ''))}")
        
        # Check token usage
        token_usage = response.get('token_usage', {})
        print(f"\n🎯 Token Usage Data:")
        print(f"  Raw data: {token_usage}")
        print(f"  Input tokens: {token_usage.get('input_tokens', 0)}")
        print(f"  Output tokens: {token_usage.get('output_tokens', 0)}")
        print(f"  Total tokens: {token_usage.get('total_tokens', 0)}")
        
        if token_usage.get('total_tokens', 0) > 0:
            print("✅ Token tracking is working!")
        else:
            print("⚠️ Token tracking shows zero - using estimation fallback")
        
        # Test trending response
        print(f"\n🔥 Testing trending response...")
        trending_response = response_gen.generate_thinking_message_response(
            user_id="test_user",
            user_input="Thị trường IT Việt Nam như thế nào?",
            conversation_history=[],
            user_intent="job_it_trending"
        )
        
        trending_tokens = trending_response.get('token_usage', {})
        print(f"🎯 Trending Token Usage:")
        print(f"  Input tokens: {trending_tokens.get('input_tokens', 0)}")
        print(f"  Output tokens: {trending_tokens.get('output_tokens', 0)}")
        print(f"  Total tokens: {trending_tokens.get('total_tokens', 0)}")
        
        # Test JSON structure for web app
        print(f"\n🌐 Web App JSON Structure Test:")
        web_response = {
            'response': response['message'],
            'token_usage': token_usage,
            'timestamp': '2024-01-01T00:00:00'
        }
        
        print(f"JSON serializable: {json.dumps(web_response, ensure_ascii=False)[:100]}...")
        
        print("\n" + "=" * 50)
        print("🎉 Token tracking test completed!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_javascript_integration():
    """Test the JavaScript integration format"""
    print("\n🔧 Testing JavaScript Integration Format")
    print("-" * 40)
    
    # Simulate what the web app should send
    mock_response = {
        'response': 'Hello! How can I help you today?',
        'token_usage': {
            'input_tokens': 15,
            'output_tokens': 25,
            'total_tokens': 40
        },
        'suggest_questions': ['What services do you offer?', 'Tell me about FOIS GROUP'],
        'user_id': 'test_user_123',
        'timestamp': '2024-01-01T00:00:00'
    }
    
    print("📤 Mock response structure:")
    print(json.dumps(mock_response, indent=2, ensure_ascii=False))
    
    print("\n📥 JavaScript should receive:")
    print(f"  data.token_usage = {mock_response['token_usage']}")
    print(f"  data.token_usage.input_tokens = {mock_response['token_usage']['input_tokens']}")
    print(f"  data.token_usage.output_tokens = {mock_response['token_usage']['output_tokens']}")
    print(f"  data.token_usage.total_tokens = {mock_response['token_usage']['total_tokens']}")

if __name__ == "__main__":
    print("🚀 FOIS Chatbot - Token Tracking Test")
    print("=" * 60)
    
    # Test token tracking
    success = test_token_tracking()
    
    # Test JavaScript integration
    test_javascript_integration()
    
    if success:
        print("\n💡 Next steps:")
        print("   1. Run: python web_app.py")
        print("   2. Open browser console (F12)")
        print("   3. Send a message and check console for token logs")
        print("   4. Click the token toggle button (📊) to show/hide stats")
    else:
        print("\n❌ Token tracking test failed. Check your configuration.")
