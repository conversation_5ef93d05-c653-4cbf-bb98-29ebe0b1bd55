# Core web framework
flask==3.0.0
flask-cors==4.0.0
werkzeug>=2.0.0

# Environment and configuration
python-dotenv==1.1.1

# HTTP requests
requests==2.31.0

# AI/ML libraries
google-generativeai>=0.8.5
google-genai

# Document processing
PyPDF2==3.0.1
python-docx==1.1.0
pdfplumber>=0.9.0

# Image processing
Pillow>=9.0.0

# File type detection
python-magic==0.4.27

# Data processing
pathlib2>=2.3.0

# Date/time handling
python-dateutil>=2.8.0

# JSON handling (usually built-in, but explicit for clarity)
# json - built-in module

# UUID generation (built-in)
# uuid - built-in module

# Type hints support
typing-extensions>=4.0.0