#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<PERSON><PERSON>u hình cho Email <PERSON>tbot
"""

import os
from dotenv import load_dotenv

# Load environment variables từ .env file
load_dotenv()

# SMTP Settings - Sử dụng environment variables
SMTP_SETTINGS = {
    "SMTP_SERVER": os.getenv("SMTP_SERVER", "smtp.gmail.com"),
    "SMTP_PORT": int(os.getenv("SMTP_PORT", "587")),
    "SENDER_MAIL": os.getenv("SMTP_SENDER_MAIL", ""),
    "PASSWORD": os.getenv("SMTP_PASSWORD", "")
}

# IMAP Settings - Sử dụng environment variables
IMAP_SETTINGS = {
    "IMAP_SERVER": os.getenv("IMAP_SERVER", "imap.gmail.com"),
    "IMAP_PORT": int(os.getenv("IMAP_PORT", "993")),
    "EMAIL": os.getenv("IMAP_EMAIL", ""),
    "PASSWORD": os.getenv("IMAP_PASSWORD", "")
}

# AI API KEY - Sử dụng environment variable
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY", "")

# Chatbot Settings - Sử dụng environment variables với default values
CHATBOT_SETTINGS = {
    # Kiểm tra email mỗi 30 giây
    "CHECK_INTERVAL": int(os.getenv("CHECK_INTERVAL", "30")),
    "USE_AI": os.getenv("USE_AI", "true").lower() == "true",   # Bật/tắt AI
    # Email để test
    "TEST_EMAIL": os.getenv("TEST_EMAIL", "<EMAIL>"),
    # Số lần thử lại khi gặp lỗi
    "MAX_RETRIES": int(os.getenv("MAX_RETRIES", "3")),
    # Timeout cho các request (giây)
    "TIMEOUT": int(os.getenv("TIMEOUT", "30")),
}

# Company Information
COMPANY_INFO = {
    "NAME": "FOIS ICT PRO",
    "FULL_NAME": "FOIS GROUP - FOIS ICT PRO",
    "EMAIL": "<EMAIL>",
    "WEBSITE": "https://asiantech.link",
    "HOTLINE": "1900-xxxx",
    "ESTABLISHED": "1991",
    "EXPERIENCE_YEARS": "33 năm",
    "SERVICES": [
        "Phát triển phần mềm",
        "Ứng dụng di động",
        "AI/ML",
        "IoT",
        "Website thương mại điện tử",
        "Tư vấn công nghệ ICT",
        "Dịch vụ ICT toàn cầu"
    ],
    "LOCATIONS": {
        "JAPAN": ["Tokyo", "Nagoya"],
        "VIETNAM": ["TP.HCM", "Hà Nội"]
    },
    "CORE_VALUES": [
        "Sáng tạo và Thách thức",
        "Quản trị toàn bộ nhân viên",
        "Quan điểm của khách hàng"
    ],
    "MISSION": "Cung cấp dịch vụ ICT từ Việt Nam và Nhật Bản ra thế giới",
    "VISION": "Trở thành một công ty thương mại chuyên nghiệp dưới góc nhìn của khách hàng",
    "DESCRIPTION": """
FOIS đặt ra "Sáng tạo và Thách thức", "Quản trị toàn bộ nhân viên" và "Quan điểm của khách hàng" là ba mục tiêu chính, tổng hợp các giá trị này và nỗ lực hàng ngày để mang lại hạnh phúc cho tất cả nhân viên và cung cấp dịch vụ tốt nhất để nhận sự hài lòng của khách hàng.

Chúng tôi đã được rất nhiều sự hỗ trợ và qua 33 năm kể từ khi thành lập vào năm 1991, chúng tôi vẫn tiếp tục phát triển và thêm nhiều nỗ lực hơn nữa cho tương lai.

Thông qua các trụ sở tại Nhật Bản (Tokyo, Nagoya) và Việt Nam (TP.HCM, Hà Nội), chúng tôi tập trung vào việc giải quyết thách thức và tạo ra giá trị bằng cách sử dụng công nghệ tiên tiến nhất. Trong một ngành công nghiệp ICT với sự tiến triển nhanh chóng của công nghệ như Trí tuệ nhân tạo (AI), IoT, chúng tôi tiến lên với sự cạnh tranh trên thị trường toàn cầu và thành lập FOIS ICT PRO tại Việt Nam và Nhật Bản.

Tất cả nhân viên của FOIS GROUP, từ Nhật Bản đến Việt Nam và các quốc gia trên thế giới, tự hào về sự hợp tác với các doanh nghiệp và tiếp tục thách thức hướng tới tương lai. Mục tiêu của chúng tôi là tạo ra giá trị thông qua sự phát triển bền vững và các phương pháp tiếp cận sáng tạo.
""".strip()
}

# AI Settings
AI_SETTINGS = {
    "MODEL_NAMES": [
        "gemini-2.5-flash",
        "gemini-1.5-pro",
        "gemini-pro"
    ],
    "MAX_TOKENS": 1000,
    "TEMPERATURE": 0.7,
    "FALLBACK_ENABLED": True
}

# Logging Settings
LOGGING_SETTINGS = {
    "LEVEL": "INFO",
    "FORMAT": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "FILE": "chatbot.log",
    "MAX_SIZE": 10 * 1024 * 1024,  # 10MB
    "BACKUP_COUNT": 5
}

# Email Templates
EMAIL_TEMPLATES = {
    "GREETING": {
        "vi": "Xin chào",
        "en": "Hello"
    },
    "THANKS": {
        "vi": "Cảm ơn bạn đã liên hệ với {company_name}",
        "en": "Thank you for contacting {company_name}"
    },
    "SIGNATURE": {
        "vi": "Trân trọng,\n{company_name} Team",
        "en": "Best regards,\n{company_name} Team"
    }
}

# Keyword Responses (fallback)
KEYWORD_RESPONSES = {
    'hello|hi|xin chào|chào|hey': {
        "vi": f"Xin chào! Cảm ơn bạn đã liên hệ với chúng tôi.\n\n"
        f"Tôi là chatbot tự động của {COMPANY_INFO['NAME']}. Tôi đã nhận được tin nhắn của bạn và sẽ chuyển đến bộ phận liên quan để xử lý.\n\n"
        f"Nếu bạn cần hỗ trợ khẩn cấp, vui lòng liên hệ hotline: {COMPANY_INFO['HOTLINE']}\n\n"
        f"Trân trọng,\n{COMPANY_INFO['NAME']} Support Team"
    },

    'support|hỗ trợ|help|giúp đỡ|technical|kỹ thuật': {
        "vi": f"Cảm ơn bạn đã liên hệ về vấn đề hỗ trợ kỹ thuật.\n\n"
        f"Chúng tôi đã ghi nhận yêu cầu của bạn và sẽ có chuyên viên kỹ thuật liên hệ trong vòng 24h.\n\n"
        f"Để được hỗ trợ nhanh hơn, bạn có thể:\n"
        f"- Truy cập trang FAQ: {COMPANY_INFO['WEBSITE']}/faq\n"
        f"- Chat trực tiếp: {COMPANY_INFO['WEBSITE']}/chat\n\n"
        f"Trân trọng,\n{COMPANY_INFO['NAME']} Technical Team"
    },

    'price|giá|báo giá|quote|cost|chi phí': {
        "vi": f"Cảm ơn bạn quan tâm đến dịch vụ của {COMPANY_INFO['NAME']}.\n\n"
        f"Chúng tôi đã nhận được yêu cầu báo giá của bạn. Đội ngũ sales sẽ liên hệ với bạn trong vòng 2-4 giờ làm việc để tư vấn chi tiết.\n\n"
        f"Trong thời gian chờ đợi, bạn có thể tham khảo:\n"
        f"- Bảng giá cơ bản: {COMPANY_INFO['WEBSITE']}/pricing\n"
        f"- Các gói dịch vụ: {COMPANY_INFO['WEBSITE']}/services\n\n"
        f"Trân trọng,\n{COMPANY_INFO['NAME']} Sales Team"
    },

    'complaint|khiếu nại|problem|vấn đề|issue|lỗi': {
        "vi": f"Chúng tôi rất tiếc khi biết bạn gặp vấn đề với dịch vụ của chúng tôi.\n\n"
        f"Khiếu nại của bạn đã được ghi nhận với mã số: #{{timestamp}}\n\n"
        f"Bộ phận chăm sóc khách hàng sẽ liên hệ với bạn trong vòng 2 giờ để giải quyết vấn đề.\n\n"
        f"Hotline khẩn cấp: {COMPANY_INFO['HOTLINE']} (24/7)\n\n"
        f"Trân trọng,\n{COMPANY_INFO['NAME']} Customer Care"
    }
}

# Default fallback response
DEFAULT_RESPONSE = {
    "vi": f"Cảm ơn bạn đã liên hệ với {COMPANY_INFO['NAME']}.\n\n"
    f"Chúng tôi đã nhận được tin nhắn của bạn và sẽ phản hồi trong thời gian sớm nhất.\n\n"
    f"Nếu cần hỗ trợ khẩn cấp, vui lòng liên hệ:\n"
    f"- Email: {COMPANY_INFO['EMAIL']}\n"
    f"- Hotline: {COMPANY_INFO['HOTLINE']}\n\n"
    f"Trân trọng,\n{COMPANY_INFO['NAME']} Team"
}

# Validation - Kiểm tra các biến môi trường quan trọng


def validate_config():
    """Kiểm tra cấu hình có đầy đủ không"""
    missing_vars = []

    if not GEMINI_API_KEY:
        missing_vars.append("GEMINI_API_KEY")

    if not SMTP_SETTINGS["SENDER_MAIL"]:
        missing_vars.append("SMTP_SENDER_MAIL")

    if not SMTP_SETTINGS["PASSWORD"]:
        missing_vars.append("SMTP_PASSWORD")

    if not IMAP_SETTINGS["EMAIL"]:
        missing_vars.append("IMAP_EMAIL")

    if not IMAP_SETTINGS["PASSWORD"]:
        missing_vars.append("IMAP_PASSWORD")

    if missing_vars:
        print("⚠️ Cảnh báo: Thiếu các biến môi trường quan trọng:")
        for var in missing_vars:
            print(f"   - {var}")
        print("Vui lòng tạo file .env và cấu hình các biến này.")
        return False

    return True
