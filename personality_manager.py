#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Personality Manager for FOIS HR Bot
Manages bot personality and response generation
"""

import random
from typing import Dict, List, Optional
from bot_personality import (
    BOT_PERSONALITY, PERSONALITY_PHRASES, PERSO<PERSON><PERSON>ITY_RESPONSES,
    EMOJI_PATTERNS, CONTEXT_RESPONSES,
    get_random_phrase, apply_personality_to_response, get_bot_introduction,
    get_personality_trait, should_use_emoji, get_random_emoji
)


class PersonalityManager:
    """Manages bot personality and response generation"""
    
    def __init__(self):
        """Initialize PersonalityManager"""
        self.personality = BOT_PERSONALITY
        self.phrases = PERSONALITY_PHRASES
        self.responses = PERSONALITY_RESPONSES
        self.emoji_patterns = EMOJI_PATTERNS
        self.context_responses = CONTEXT_RESPONSES
    
    def generate_greeting(self, is_first_time: bool = False) -> str:
        """Generate greeting based on user status"""
        if is_first_time:
            greeting = self.context_responses["first_time_user"]["welcome"]
            intro = self.context_responses["first_time_user"]["introduction"]
            return f"{greeting}\n\n{intro}"
        else:
            return random.choice(self.context_responses["returning_user"]["welcome"])
    
    def apply_personality_to_response(self, response: str, context: str = "general", 
                                    user_sentiment: str = "neutral", user_input: str = "") -> str:
        """Apply personality traits to response"""
        # Use the existing function from bot_personality
        enhanced_response = apply_personality_to_response(response, context)
        
        # Add context-specific enhancements
        if context == "job_search" and get_personality_trait("enthusiasm") >= 8:
            job_phrase = random.choice(self.phrases["job_related"])
            if len(enhanced_response) < 300:  # Don't make it too long
                enhanced_response += f"\n\n{job_phrase}"
        
        return enhanced_response
    
    def detect_emotional_keywords(self, user_input: str) -> str:
        """Detect emotional state from user input"""
        user_input_lower = user_input.lower()
        
        # Check for unemployment keywords
        unemployment_keywords = ["thất nghiệp", "mất việc", "bị sa thải", "không có việc", 
                               "unemployed", "jobless", "laid off", "fired"]
        if any(keyword in user_input_lower for keyword in unemployment_keywords):
            return "unemployed"
        
        # Check for sad/worried keywords
        sad_keywords = ["buồn", "lo lắng", "stress", "áp lực", "khó khăn", "sad", "worried", 
                       "stressed", "difficult", "hard", "tough"]
        if any(keyword in user_input_lower for keyword in sad_keywords):
            return "sad"
        
        # Check for frustrated keywords
        frustrated_keywords = ["bực", "tức", "khó chịu", "frustrated", "annoyed", "angry"]
        if any(keyword in user_input_lower for keyword in frustrated_keywords):
            return "frustrated"
        
        # Check for hopeless keywords
        hopeless_keywords = ["tuyệt vọng", "không còn hy vọng", "hopeless", "desperate"]
        if any(keyword in user_input_lower for keyword in hopeless_keywords):
            return "hopeless"
        
        return "neutral"
    
    def generate_unemployment_response(self, user_input: str) -> str:
        """Generate empathetic response for unemployed users"""
        empathy_phrases = [
            "Mình hiểu việc tìm kiếm công việc mới có thể rất căng thẳng 😔",
            "Thời gian này chắc hẳn không dễ dàng với bạn 💙",
            "Mình cảm thông với hoàn cảnh hiện tại của bạn 🤗"
        ]
        
        encouragement_phrases = [
            "Nhưng đừng lo lắng, mình tin bạn sẽ tìm được công việc phù hợp sớm thôi! 💪",
            "Hãy cùng mình tìm kiếm cơ hội mới nhé! ✨",
            "Mình sẽ giúp bạn tìm được vị trí tuyệt vời! 🌟"
        ]
        
        empathy = random.choice(empathy_phrases)
        encouragement = random.choice(encouragement_phrases)
        
        response = f"{empathy} {encouragement}\n\n"
        response += "🎯 **Mình có thể giúp bạn:**\n"
        response += "- 🔍 Tìm kiếm job opportunities phù hợp\n"
        response += "- 📄 Phân tích CV để tối ưu hóa\n"
        response += "- 💰 Tư vấn mức lương cạnh tranh\n"
        response += "- 🏢 Giới thiệu về FOIS GROUP - nơi có môi trường tuyệt vời\n"
        response += "- 💡 Hướng dẫn chuẩn bị phỏng vấn\n\n"
        response += "Bạn muốn bắt đầu từ đâu nhỉ? Mình sẽ đồng hành cùng bạn! 🤝"
        
        return response
    
    def generate_empathetic_response(self, user_input: str, base_response: str) -> str:
        """Generate empathetic response for emotional users"""
        empathy_starters = [
            "Mình hiểu cảm giác của bạn 💙",
            "Điều đó nghe có vẻ khó khăn nhỉ 😔",
            "Mình cảm thông với bạn 🤗"
        ]
        
        empathy = random.choice(empathy_starters)
        return f"{empathy}\n\n{base_response}"
    
    def detect_off_topic_keywords(self, user_input: str) -> bool:
        """Detect if question is completely off-topic"""
        off_topic_keywords = [
            "thời tiết", "weather", "ăn gì", "what to eat", "phim", "movie",
            "âm nhạc", "music", "thể thao", "sports", "chính trị", "politics",
            "tình yêu", "love", "hẹn hò", "dating", "nấu ăn", "cooking"
        ]
        
        user_input_lower = user_input.lower()
        return any(keyword in user_input_lower for keyword in off_topic_keywords)
    
    def generate_redirect_response(self, user_input: str, response_type: str = "redirect") -> str:
        """Generate polite redirect response for off-topic questions"""
        redirect_phrases = [
            "Ôi, câu hỏi thú vị đấy! 😄 Nhưng mình chuyên về tuyển dụng và HR thôi nhé!",
            "Haha, bạn hỏi hay quá! 😊 Tuy nhiên mình chỉ giỏi về việc làm và nghề nghiệp thôi!",
            "Câu hỏi dễ thương! 😄 Nhưng mình là HR bot nên chỉ biết về job và career thôi!"
        ]
        
        redirect = random.choice(redirect_phrases)
        
        response = f"{redirect}\n\n"
        response += "🎯 **Mình có thể giúp bạn về:**\n"
        response += "- 🔍 Tìm kiếm cơ hội việc làm\n"
        response += "- 🏢 Thông tin về FOIS GROUP\n"
        response += "- 💰 Mức lương theo vị trí\n"
        response += "- 📄 Phân tích và tối ưu CV\n"
        response += "- 💡 Tư vấn phát triển sự nghiệp\n\n"
        response += "Bạn có muốn khám phá điều gì về nghề nghiệp không? 😊"
        
        return response
