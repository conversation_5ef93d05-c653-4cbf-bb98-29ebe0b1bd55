#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for conversation history functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from new_response_generator import NewResponseGenerator
from gemini_ai import GeminiAI
import json

def test_conversation_history():
    """Test conversation history with both user and bot messages"""
    
    print("🧪 Testing Conversation History Functionality")
    print("=" * 60)
    
    try:
        # Initialize components
        print("📝 Initializing AI components...")
        gemini_ai = GeminiAI()
        response_gen = NewResponseGenerator(gemini_ai)
        
        print("✅ Components initialized successfully!")
        
        # Simulate a conversation
        conversation_history = []
        
        print("\n🎭 Simulating Multi-Turn Conversation:")
        print("-" * 40)
        
        # Turn 1: User asks about company
        user_msg_1 = "Tell me about FOIS GROUP"
        print(f"👤 User: {user_msg_1}")
        
        response_1 = response_gen.generate_response(
            user_id="test_user",
            user_input=user_msg_1,
            conversation_history=conversation_history
        )
        
        bot_msg_1 = response_1.get('message', '')
        print(f"🤖 Bot: {bot_msg_1[:100]}...")
        
        # Add to conversation history
        conversation_history.append({
            'type': 'user',
            'message': user_msg_1,
            'timestamp': '2024-01-01T00:00:00'
        })
        conversation_history.append({
            'type': 'bot', 
            'message': bot_msg_1,
            'timestamp': '2024-01-01T00:01:00'
        })
        
        print(f"\n📊 Conversation history length: {len(conversation_history)}")
        
        # Turn 2: User asks follow-up question
        user_msg_2 = "What services do you offer?"
        print(f"\n👤 User: {user_msg_2}")
        
        response_2 = response_gen.generate_response(
            user_id="test_user",
            user_input=user_msg_2,
            conversation_history=conversation_history
        )
        
        bot_msg_2 = response_2.get('message', '')
        print(f"🤖 Bot: {bot_msg_2[:100]}...")
        
        # Add to conversation history
        conversation_history.append({
            'type': 'user',
            'message': user_msg_2,
            'timestamp': '2024-01-01T00:02:00'
        })
        conversation_history.append({
            'type': 'bot',
            'message': bot_msg_2,
            'timestamp': '2024-01-01T00:03:00'
        })
        
        print(f"\n📊 Conversation history length: {len(conversation_history)}")
        
        # Turn 3: User asks about previous topic
        user_msg_3 = "Can you tell me more about what you just mentioned?"
        print(f"\n👤 User: {user_msg_3}")
        
        response_3 = response_gen.generate_response(
            user_id="test_user",
            user_input=user_msg_3,
            conversation_history=conversation_history
        )
        
        bot_msg_3 = response_3.get('message', '')
        print(f"🤖 Bot: {bot_msg_3[:100]}...")
        
        print(f"\n📊 Final conversation history length: {len(conversation_history)}")
        
        # Analyze conversation history structure
        print(f"\n🔍 Conversation History Analysis:")
        print("-" * 30)
        for i, msg in enumerate(conversation_history):
            msg_type = msg.get('type', 'unknown')
            msg_preview = msg.get('message', '')[:50] + "..."
            print(f"  {i+1}. [{msg_type.upper()}] {msg_preview}")
        
        # Test token usage tracking
        print(f"\n💰 Token Usage Analysis:")
        print("-" * 25)
        for i, response in enumerate([response_1, response_2, response_3], 1):
            tokens = response.get('token_usage', {})
            print(f"  Turn {i}: {tokens.get('total_tokens', 0)} tokens")
        
        # Test context awareness
        print(f"\n🧠 Context Awareness Test:")
        print("-" * 25)
        
        # Check if the bot references previous conversation
        context_keywords = ['mentioned', 'said', 'discussed', 'talked about', 'earlier', 'before']
        has_context = any(keyword in bot_msg_3.lower() for keyword in context_keywords)
        
        if has_context:
            print("✅ Bot shows context awareness (references previous conversation)")
        else:
            print("⚠️ Bot may not be using conversation context effectively")
        
        print("\n" + "=" * 60)
        print("🎉 Conversation history test completed!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_conversation_structure():
    """Test the conversation history data structure"""
    print("\n🏗️ Testing Conversation Structure")
    print("-" * 35)
    
    # Test structured conversation history
    test_history = [
        {'type': 'user', 'message': 'Hello', 'timestamp': '2024-01-01T00:00:00'},
        {'type': 'bot', 'message': 'Hi there!', 'timestamp': '2024-01-01T00:00:01'},
        {'type': 'user', 'message': 'How are you?', 'timestamp': '2024-01-01T00:00:02'},
        {'type': 'bot', 'message': 'I am doing well!', 'timestamp': '2024-01-01T00:00:03'}
    ]
    
    print("📋 Test conversation structure:")
    for i, msg in enumerate(test_history):
        print(f"  {i+1}. {msg}")
    
    # Test role mapping
    print("\n🎭 Role mapping test:")
    for msg in test_history:
        role = "user" if msg.get('type') == 'user' else "model"
        print(f"  {msg.get('type')} -> {role}")
    
    print("✅ Conversation structure test passed!")

if __name__ == "__main__":
    print("🚀 FOIS Chatbot - Conversation History Test")
    print("=" * 70)
    
    # Test conversation structure
    test_conversation_structure()
    
    # Test conversation history functionality
    success = test_conversation_history()
    
    if success:
        print("\n💡 Key Improvements:")
        print("   ✅ Bot responses are now saved to conversation history")
        print("   ✅ Both user and assistant messages are passed to AI")
        print("   ✅ Conversation context is maintained across turns")
        print("   ✅ System prompts are excluded from history")
        print("   ✅ Token usage is tracked for each turn")
        
        print("\n🎯 Expected Benefits:")
        print("   - More contextual and relevant responses")
        print("   - Better follow-up question handling")
        print("   - Reduced repetitive responses")
        print("   - Improved conversation flow")
    else:
        print("\n❌ Conversation history test failed. Check your configuration.")
