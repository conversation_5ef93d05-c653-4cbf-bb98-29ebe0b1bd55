#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Response Generator cho Email Chatbot
Tạo phản hồi dựa trên intent và context
"""

from typing import Dict, List, Optional
from intent_detector import IntentType, IntentResult
from context_manager import ContextManager
from gemini_ai import GeminiAI
from config import COMPANY_INFO

from sample_data import (
    SAMPLE_JOBS, SALARY_RANGES, PLATFORM_FEATURES,
    FEEDBACK_RESPONSES, FOLLOW_UP_CONTEXTS,
    NOT_INTERESTED_RESPONSES, OTHER_RESPONSES
)


class ResponseGenerator:
    """Class tạo phản hồi dựa trên intent"""

    def __init__(self, gemini_ai: GeminiAI, context_manager: ContextManager):
        """Khởi tạo Response Generator"""
        self.gemini_ai = gemini_ai
        self.context_manager = context_manager

        self.response_templates = self._load_response_templates()
        self.job_database = self._load_sample_jobs()

    def _load_response_templates(self) -> Dict[IntentType, str]:
        """Load template phản hồi cho từng intent"""
        return {
            IntentType.GREETINGS: """
Xin chào! Tôi là AI Assistant của {company_name}.
Tôi có thể giúp bạn:
- Tìm hiểu về công ty
- Tìm kiếm cơ hội việc làm
- Hướng dẫn sử dụng hệ thống
- Tư vấn về mức lương

Bạn cần hỗ trợ gì hôm nay?
""",

            IntentType.ASK_COMPANY_INFO: """
{company_name} là một tập đoàn công nghệ ICT hàng đầu với {experience_years} kinh nghiệm, được thành lập từ năm {established}.

🏢 **Về FOIS GROUP:**
- Trụ sở: Nhật Bản ({japan_locations}) và Việt Nam ({vietnam_locations})
- Sứ mệnh: {mission}
- Tầm nhìn: {vision}

🎯 **Giá trị cốt lõi:**
{core_values}

💼 **Dịch vụ chuyên nghiệp:**
{services}

{description}

🌐 Website: {website}
📧 Liên hệ: {email}
""",

            IntentType.ASK_PLATFORM_USAGE: """
Hướng dẫn sử dụng hệ thống {company_name}:

1. **Tìm việc**: Nói với tôi về kỹ năng và vị trí mong muốn
2. **Xem chi tiết job**: Hỏi về job cụ thể
3. **Gửi CV**: Chia sẻ thông tin hồ sơ của bạn
4. **Tư vấn lương**: Hỏi về mức lương theo vị trí

Ví dụ: "Tôi muốn tìm việc Python Developer" hoặc "Mức lương Backend Engineer bao nhiêu?"
""",

            IntentType.NOT_INTERESTED: """
Cảm ơn bạn đã dành thời gian! 

Nếu sau này bạn có nhu cầu tìm việc hoặc muốn tìm hiểu về cơ hội nghề nghiệp, 
đừng ngần ngại liên hệ lại với chúng tôi.

Chúc bạn thành công trong công việc hiện tại!
""",

            IntentType.OTHER: """
Xin lỗi, tôi chưa hiểu rõ yêu cầu của bạn.

Tôi có thể giúp bạn:
- Tìm việc làm phù hợp
- Thông tin về công ty
- Hướng dẫn sử dụng hệ thống
- Tư vấn mức lương

Bạn có thể nói rõ hơn về điều bạn cần không?
"""
        }

    def _load_sample_jobs(self) -> List[Dict]:
        """Load sample job database"""
        return [
            {
                "id": "job_001",
                "title": "Senior Python Developer",
                "company": "TechCorp Vietnam",
                "salary": "25-35 triệu",
                "location": "Hà Nội",
                "remote": True,
                "skills": ["Python", "Django", "PostgreSQL", "Docker"],
                "requirements": "3+ năm kinh nghiệm Python, biết Django framework"
            },
            {
                "id": "job_002",
                "title": "Frontend React Developer",
                "company": "StartupXYZ",
                "salary": "20-30 triệu",
                "location": "TP.HCM",
                "remote": False,
                "skills": ["React", "JavaScript", "TypeScript", "CSS"],
                "requirements": "2+ năm kinh nghiệm React, thành thạo JavaScript"
            },
            {
                "id": "job_003",
                "title": "DevOps Engineer",
                "company": "CloudTech",
                "salary": "30-40 triệu",
                "location": "Remote",
                "remote": True,
                "skills": ["AWS", "Docker", "Kubernetes", "CI/CD"],
                "requirements": "3+ năm kinh nghiệm DevOps, biết AWS/Azure"
            }
        ]

    def generate_response(self, user_id: str, user_input: str,
                          intent_result: IntentResult) -> str:
        """Tạo phản hồi dựa trên intent"""

        if intent_result.intent == IntentType.FOLLOW_UP:
            return self._handle_follow_up(user_id, user_input, intent_result)

        # Xử lý các intent khác
        handler_map = {
            IntentType.GREETINGS: self._handle_greetings,
            IntentType.ASK_COMPANY_INFO: self._handle_company_info,
            IntentType.ASK_PLATFORM_USAGE: self._handle_platform_usage,
            IntentType.ASK_JOB_OPPORTUNITIES: self._handle_job_opportunities,
            IntentType.ASK_JOB_DETAILS: self._handle_job_details,
            IntentType.SHARE_PROFILE: self._handle_share_profile,
            IntentType.NOT_INTERESTED: self._handle_not_interested,
            IntentType.SALARY_EXPECTATION: self._handle_salary_expectation,
            IntentType.FEEDBACK_ON_JOB_MATCH: self._handle_job_feedback,
            IntentType.OTHER: self._handle_other
        }

        handler = handler_map.get(intent_result.intent, self._handle_other)
        return handler(user_id, user_input, intent_result)

    def _handle_greetings(self, user_id: str, user_input: str, intent_result: IntentResult) -> str:
        """Xử lý intent GREETINGS"""
        # Kiểm tra xem có phải lần đầu gặp user không
        is_first_time = not self.context_manager.has_context(user_id)

        # Tạo greeting với personality
        greeting = self.personality.generate_greeting(is_first_time)

        # Apply personality
        greeting = self.personality.apply_personality_to_response(
            greeting,
            context="greeting",
            user_sentiment="neutral"
        )

        return greeting

    def _handle_company_info(self, user_id: str, user_input: str, intent_result: IntentResult) -> str:
        """Xử lý intent ASK_COMPANY_INFO"""
        template = self.response_templates[IntentType.ASK_COMPANY_INFO]
        response = template.format(
            company_name=COMPANY_INFO['FULL_NAME'],
            established=COMPANY_INFO['ESTABLISHED'],
            experience_years=COMPANY_INFO['EXPERIENCE_YEARS'],
            japan_locations=', '.join(COMPANY_INFO['LOCATIONS']['JAPAN']),
            vietnam_locations=', '.join(COMPANY_INFO['LOCATIONS']['VIETNAM']),
            mission=COMPANY_INFO['MISSION'],
            vision=COMPANY_INFO['VISION'],
            core_values='\n'.join(
                [f"- {value}" for value in COMPANY_INFO['CORE_VALUES']]),
            services='\n'.join(
                [f"- {service}" for service in COMPANY_INFO['SERVICES']]),
            description=COMPANY_INFO['DESCRIPTION'],
            website=COMPANY_INFO['WEBSITE'],
            email=COMPANY_INFO['EMAIL']
        ).strip()

        # Apply personality
        response = self.personality.apply_personality_to_response(
            response,
            context="company",
            user_sentiment="neutral"
        )

        return response

    def _handle_platform_usage(self, user_id: str, user_input: str, intent_result: IntentResult) -> str:
        """Xử lý intent ASK_PLATFORM_USAGE với dữ liệu mẫu"""
        response = f"📚 **Hướng dẫn sử dụng hệ thống {COMPANY_INFO['NAME']}:**\n\n"

        # Hiển thị các tính năng từ dữ liệu mẫu
        for feature_key, feature_info in PLATFORM_FEATURES.items():
            response += f"🎯 **{feature_info['title']}**\n"
            response += f"   {feature_info['description']}\n\n"
            response += "   📋 **Các bước:**\n"
            for i, step in enumerate(feature_info['steps'], 1):
                response += f"   {i}. {step}\n"
            response += "\n"

        response += "💡 **Tips:**\n"
        response += "- Tạo profile đầy đủ để tăng cơ hội được nhà tuyển dụng chú ý\n"
        response += "- Cập nhật CV thường xuyên\n"
        response += "- Sử dụng từ khóa phù hợp khi tìm kiếm\n"
        response += "- Theo dõi status application thường xuyên\n\n"
        response += "❓ Bạn cần hướng dẫn cụ thể về tính năng nào?"

        return response

    def _handle_job_opportunities(self, user_id: str, user_input: str, intent_result: IntentResult) -> str:
        """Xử lý intent ASK_JOB_OPPORTUNITIES"""
        # Lọc job dựa trên entities
        skills = intent_result.entities.get('skills', '').lower()
        position = intent_result.entities.get('position', '').lower()

        # Sử dụng dữ liệu mẫu thay vì job_database
        matching_jobs = []
        for job in SAMPLE_JOBS:
            job_skills = [s.lower() for s in job['skills']]
            job_title = job['title'].lower()
            job_location = job['location'].lower()

            match_score = 0

            # Kiểm tra skills
            if skills and any(skill in job_skills for skill in skills.split(', ')):
                match_score += 3

            # Kiểm tra position
            if position and position in job_title:
                match_score += 2

            # Kiểm tra location
            location = intent_result.entities.get('location', '').lower()
            if location and (location in job_location or job['remote']):
                match_score += 1

            if match_score > 0:
                matching_jobs.append((job, match_score))

        # Sắp xếp theo điểm match
        matching_jobs.sort(key=lambda x: x[1], reverse=True)
        matching_jobs = [job for job, score in matching_jobs]

        if not matching_jobs:
            matching_jobs = SAMPLE_JOBS[:3]  # Hiển thị 3 job đầu

        # Lưu job recommendations vào context
        self.context_manager.update_job_recommendations(user_id, matching_jobs)

        response = "🎯 Đây là những cơ hội việc làm phù hợp với bạn:\n\n"
        for i, job in enumerate(matching_jobs[:3], 1):
            response += f"**{i}. {job['title']}** tại {job['company']}\n"
            response += f"   💰 Lương: {job['salary_range']}\n"
            response += f"   📍 Địa điểm: {job['location']}"
            if job['remote']:
                response += " (Remote OK)"
            response += f"\n   🎯 Kỹ năng: {', '.join(job['skills'][:3])}\n"
            response += f"   ⏰ Kinh nghiệm: {job['experience']}\n"
            response += f"   📋 Loại: {job['type']}\n\n"

        response += "Bạn muốn xem chi tiết job nào? Hoặc có câu hỏi gì khác không?"

        # Apply personality với job enthusiasm
        response = self.personality.apply_personality_to_response(
            response,
            context="job_search",
            user_sentiment="neutral"
        )

        return response

    def _handle_job_details(self, user_id: str, user_input: str, intent_result: IntentResult) -> str:
        """Xử lý intent ASK_JOB_DETAILS"""
        last_job = self.context_manager.get_last_job_discussed(user_id)

        if not last_job:
            return "Bạn chưa xem job nào. Hãy tìm kiếm job trước nhé!"

        response = f"**Chi tiết {last_job['title']}**\n\n"
        response += f"🏢 Công ty: {last_job['company']}\n"
        response += f"💰 Mức lương: {last_job['salary']}\n"
        response += f"📍 Địa điểm: {last_job['location']}\n"
        response += f"🏠 Remote: {'Có' if last_job['remote'] else 'Không'}\n"
        response += f"🛠️ Kỹ năng: {', '.join(last_job['skills'])}\n"
        response += f"📋 Yêu cầu: {last_job['requirements']}\n\n"
        response += "Bạn có muốn ứng tuyển vị trí này không?"

        return response

    def _handle_share_profile(self, user_id: str, user_input: str, intent_result: IntentResult) -> str:
        """Xử lý intent SHARE_PROFILE"""
        return """
Tuyệt vời! Để gửi hồ sơ, bạn có thể:

1. **Gửi CV qua email**: Attach CV vào email reply
2. **Chia sẻ thông tin**: Nói với tôi về kinh nghiệm và kỹ năng
3. **LinkedIn Profile**: Gửi link LinkedIn của bạn

Ví dụ: "Tôi có 3 năm kinh nghiệm Python, từng làm tại ABC Company"

Bạn muốn chia sẻ thông tin như thế nào?
"""

    def _handle_not_interested(self, user_id: str, user_input: str, intent_result: IntentResult) -> str:
        """Xử lý intent NOT_INTERESTED với dữ liệu mẫu"""
        import random
        response = random.choice(NOT_INTERESTED_RESPONSES)

        # Lưu trạng thái không quan tâm vào context
        self.context_manager.add_context(user_id, {
            'intent': 'not_interested',
            'timestamp': 'now',
            'status': 'inactive'
        })

        return response

    def _handle_salary_expectation(self, user_id: str, user_input: str, intent_result: IntentResult) -> str:
        """Xử lý intent SALARY_EXPECTATION với dữ liệu mẫu"""
        # Kiểm tra có position cụ thể không
        position = intent_result.entities.get('position', '').lower()
        skills = intent_result.entities.get('skills', '').lower()

        response = "💰 **Thống kê mức lương tại Việt Nam:**\n\n"

        # Hiển thị salary ranges từ dữ liệu mẫu
        for role_key, salary_info in SALARY_RANGES.items():
            role_name = role_key.replace('_', ' ').title()
            response += f"**{role_name}**\n"
            response += f"   💵 {salary_info['range']}\n"
            response += f"   📝 {salary_info['description']}\n"
            response += f"   🎯 Kỹ năng: {', '.join(salary_info['skills'])}\n\n"

        # Nếu có position cụ thể, highlight relevant salary
        if position or skills:
            response += "🎯 **Dựa trên yêu cầu của bạn:**\n"

            # Tìm salary range phù hợp từ SAMPLE_JOBS
            relevant_jobs = []
            for job in SAMPLE_JOBS:
                job_match = False
                if position and position in job['title'].lower():
                    job_match = True
                if skills and any(skill in [s.lower() for s in job['skills']] for skill in skills.split()):
                    job_match = True

                if job_match:
                    relevant_jobs.append(job)

            if relevant_jobs:
                response += f"Các vị trí tương tự có mức lương:\n"
                for job in relevant_jobs[:3]:
                    response += f"- {job['title']}: {job['salary_range']}\n"

        response += "\n💡 Bạn muốn biết thêm về mức lương cho vị trí nào cụ thể?"

        return response

    def _handle_job_feedback(self, user_id: str, user_input: str, intent_result: IntentResult) -> str:
        """Xử lý intent FEEDBACK_ON_JOB_MATCH với dữ liệu mẫu"""
        import random

        # Phân tích loại feedback
        feedback_type = "general"
        if "lương" in user_input.lower() or "salary" in user_input.lower():
            feedback_type = "salary_too_low"
        elif "địa điểm" in user_input.lower() or "location" in user_input.lower():
            feedback_type = "location_issue"
        elif "không phù hợp" in user_input.lower() or "not suitable" in user_input.lower():
            feedback_type = "not_suitable"

        # Chọn response phù hợp
        if feedback_type in FEEDBACK_RESPONSES:
            base_response = random.choice(FEEDBACK_RESPONSES[feedback_type])
        else:
            base_response = random.choice(FEEDBACK_RESPONSES["not_suitable"])

        response = f"{base_response}\n\n"
        response += "🔍 **Để tìm job phù hợp hơn:**\n"
        response += "- Kỹ năng chính của bạn\n"
        response += "- Vị trí mong muốn\n"
        response += "- Mức lương kỳ vọng\n"
        response += "- Địa điểm làm việc ưa thích\n\n"

        # Gợi ý job alternatives
        response += "💡 **Gợi ý job khác:**\n"
        alternative_jobs = SAMPLE_JOBS[:2]  # Lấy 2 job đầu làm alternative
        for i, job in enumerate(alternative_jobs, 1):
            response += f"{i}. {job['title']} - {job['salary_range']} - {job['location']}\n"

        response += "\n📝 Ví dụ: 'Tôi muốn tìm job React Developer, lương 20-25 triệu, làm remote'"

        return response

    def _handle_follow_up(self, user_id: str, user_input: str, intent_result: IntentResult) -> str:
        """Xử lý intent FOLLOW_UP"""
        # Lấy context cuộc hội thoại trước
        context = self.context_manager.get_follow_up_context(user_id)

        if not context:
            return "Xin lỗi, tôi không nhớ chúng ta đang nói về gì. Bạn có thể nhắc lại không?"

        # Sử dụng Gemini AI để xử lý follow-up với context
        if self.gemini_ai.is_available():
            try:
                prompt = f"""
[BỐI CẢNH HỘI THOẠI TRƯỚC]:
{context}

Ứng viên hỏi tiếp: "{user_input}"

Dựa trên hội thoại trước, hãy trả lời câu hỏi mới một cách tự nhiên và hữu ích.
Nếu họ hỏi về job, hãy tham khảo thông tin job đã thảo luận trước đó.
"""

                return self.gemini_ai.generate_response(prompt, user_id, "Follow-up question")
            except Exception as e:
                print(f"Lỗi xử lý follow-up với AI: {str(e)}")

        # Fallback response
        return f"Dựa trên cuộc trò chuyện trước:\n{context}\n\nBạn có thể hỏi cụ thể hơn được không?"

    def _handle_other(self, user_id: str, user_input: str, intent_result: IntentResult) -> str:
        """Xử lý intent OTHER với empathy và gentle redirect"""
        import random

        # Detect emotional state from user input
        emotion = self.personality.detect_emotional_keywords(user_input)

        if emotion == "unemployed":
            # Special response for unemployed users with specific job suggestions
            unemployment_response = self.personality.generate_unemployment_response(
                user_input)
            return unemployment_response

        elif emotion in ["sad", "worried", "frustrated", "hopeless"]:
            # Generate empathetic response for other emotional users
            base_response = "Mình có thể giúp bạn:\n\n🔍 Tìm kiếm cơ hội việc làm phù hợp\n🏢 Tìm hiểu về FOIS GROUP - nơi có môi trường làm việc tuyệt vời\n💰 Thông tin mức lương cạnh tranh\n📄 Phân tích CV để tìm job match hoàn hảo\n💡 Tư vấn phát triển sự nghiệp\n\nBạn muốn bắt đầu từ đâu nhỉ? Mình sẽ đồng hành cùng bạn! 🤗"

            empathetic_response = self.personality.generate_empathetic_response(
                user_input, base_response)
            return empathetic_response

        # Check if question is completely off-topic
        elif self.personality.detect_off_topic_keywords(user_input):
            # Generate polite redirect response for off-topic questions
            redirect_response = self.personality.generate_redirect_response(
                user_input, "redirect")
            return redirect_response

        else:
            # Standard response for general HR-related queries
            friendly_responses = [
                "Mình hiểu bạn có thể có nhiều câu hỏi! 😊",
                "Ôi, có vẻ như bạn cần hỗ trợ về điều gì đó khác nhỉ? 🤔",
                "Hmm, câu hỏi thú vị đấy! Mình sẽ cố gắng hỗ trợ bạn! 💪"
            ]

            response = random.choice(friendly_responses)
            response += "\n\n🎯 **Mình có thể giúp bạn những điều này:**\n"
            response += "- 🔍 Tìm job opportunities tuyệt vời\n"
            response += "- 🏢 Thông tin về FOIS GROUP\n"
            response += "- 💰 Mức lương theo vị trí\n"
            response += "- 📋 Hướng dẫn sử dụng platform\n"
            response += "- 📄 Upload CV và nhận gợi ý job\n\n"
            response += "💡 Bạn muốn khám phá điều gì trước nhỉ? Mình rất háo hức được hỗ trợ! ✨"

            # Apply personality
            response = self.personality.apply_personality_to_response(
                response,
                context="help",
                user_sentiment="neutral",
                user_input=user_input
            )

            return response
