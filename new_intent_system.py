#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
New Intent System - 3 Customer Types with 10 Sub-intents Each
"""

from enum import Enum
from dataclasses import dataclass
from typing import List, Dict, Optional


class CustomerType(Enum):
    """3 loại khách hàng chính"""
    CURIOUS = "curious"           # KH không quan tâm, chỉ tò mò
    JOB_SEEKER = "job_seeker"     # KH có quan tâm, đang tìm việc
    PRIVATE = "private"           # KH có quan tâm nhưng không muốn chia sẻ


class CuriousIntent(Enum):
    """12 intent cho khách hàng tò mò"""
    GENERAL_GREETING = "curious_greeting"           # Chào hỏi chung
    COMPANY_INFO = "curious_company_info"           # Hỏi về công ty chung
    COMPANY_CULTURE = "curious_culture"             # Hỏi về văn hóa công ty
    CAREER_DEVELOPMENT = "curious_career"           # Hỏi về cơ hội phát triển
    WEBSITE_BROWSING = "curious_browsing"           # Duyệt web tò mò
    RANDOM_QUESTIONS = "curious_random"             # Câu hỏi ngẫu nhiên
    TECHNOLOGY_INTEREST = "curious_tech"            # Tò mò về công nghệ
    INDUSTRY_QUESTIONS = "curious_industry"         # Hỏi về ngành
    CASUAL_CHAT = "curious_chat"                    # Trò chuyện phiếm
    COMPARISON_QUESTIONS = "curious_compare"        # So sánh với công ty khác
    NEWS_UPDATES = "curious_news"                   # Tin tức, cập nhật
    GOODBYE = "curious_goodbye"                     # Tạm biệt


class JobSeekerIntent(Enum):
    """10 intent cho người tìm việc"""
    JOB_SEARCH = "seeker_job_search"               # Tìm kiếm việc làm
    SALARY_INQUIRY = "seeker_salary"               # Hỏi về lương
    CV_SUBMISSION = "seeker_cv"                    # Nộp CV
    INTERVIEW_PREP = "seeker_interview"            # Chuẩn bị phỏng vấn
    SKILL_REQUIREMENTS = "seeker_skills"           # Yêu cầu kỹ năng
    CAREER_ADVICE = "seeker_advice"                # Tư vấn nghề nghiệp
    APPLICATION_STATUS = "seeker_status"           # Trạng thái ứng tuyển
    WORK_ENVIRONMENT = "seeker_environment"        # Môi trường làm việc
    BENEFITS_INQUIRY = "seeker_benefits"           # Phúc lợi
    URGENT_HIRING = "seeker_urgent"                # Tuyển dụng gấp


class PrivateIntent(Enum):
    """10 intent cho khách hàng kín đáo"""
    ANONYMOUS_INQUIRY = "private_anonymous"         # Hỏi ẩn danh
    GENERAL_OPPORTUNITIES = "private_opportunities"  # Cơ hội chung
    MARKET_RESEARCH = "private_research"           # Nghiên cứu thị trường
    INDIRECT_QUESTIONS = "private_indirect"        # Câu hỏi gián tiếp
    HYPOTHETICAL_SCENARIOS = "private_hypothetical"  # Tình huống giả định
    FRIEND_INQUIRY = "private_friend"              # Hỏi cho bạn
    FUTURE_PLANNING = "private_planning"           # Lập kế hoạch tương lai
    INDUSTRY_TRENDS = "private_trends"             # Xu hướng ngành
    SKILL_DEVELOPMENT = "private_skill_dev"        # Phát triển kỹ năng
    CAUTIOUS_EXPLORATION = "private_exploration"   # Khám phá thận trọng


@dataclass
class IntentResult:
    """Kết quả phân tích intent"""
    customer_type: CustomerType
    specific_intent: Enum
    confidence: float
    reasoning: str
    suggested_response_style: str


class IntentClassifier:
    """AI-based Intent Classifier"""

    def __init__(self, gemini_ai):
        self.gemini_ai = gemini_ai
        self.intent_definitions = self._load_intent_definitions()

    def _load_intent_definitions(self) -> Dict:
        """Load intent definitions and examples"""
        return {
            CustomerType.CURIOUS: {
                "description": "Khách hàng tò mò, không có nhu cầu tìm việc cụ thể, chỉ muốn tìm hiểu",
                "characteristics": [
                    "Câu hỏi chung chung về công ty",
                    "Không đề cập đến việc làm cụ thể",
                    "Tò mò về công nghệ, ngành nghề",
                    "Thái độ casual, không gấp gáp"
                ],
                "examples": [
                    "Công ty bạn làm gì vậy?",
                    "FOIS GROUP có bao nhiêu nhân viên?",
                    "Ngành IT hiện tại thế nào?",
                    "Tôi chỉ tò mò thôi"
                ]
            },
            CustomerType.JOB_SEEKER: {
                "description": "Khách hàng đang tích cực tìm việc, sẵn sàng chia sẻ thông tin",
                "characteristics": [
                    "Hỏi trực tiếp về việc làm",
                    "Quan tâm lương, phúc lợi",
                    "Muốn nộp CV, ứng tuyển",
                    "Cần tư vấn nghề nghiệp"
                ],
                "examples": [
                    "Tôi muốn tìm việc",
                    "Có vị trí nào phù hợp không?",
                    "Lương bao nhiêu?",
                    "Tôi có thể nộp CV không?"
                ]
            },
            CustomerType.PRIVATE: {
                "description": "Khách hàng có quan tâm nhưng thận trọng, không muốn chia sẻ thông tin cá nhân",
                "characteristics": [
                    "Hỏi gián tiếp, ẩn danh",
                    "Dùng từ 'bạn tôi', 'một người nào đó'",
                    "Quan tâm nhưng không cam kết",
                    "Muốn thông tin nhưng giữ kín"
                ],
                "examples": [
                    "Bạn tôi muốn biết...",
                    "Nếu như có người...",
                    "Tôi chỉ hỏi thôi, không ứng tuyển",
                    "Có thể cho biết thông tin chung không?"
                ]
            }
        }

    def classify_intent(self, user_input: str, conversation_history: List[str] = None) -> IntentResult:
        """Classify user intent using AI"""

        # Prepare context for AI
        context = self._prepare_context(user_input, conversation_history)

        # Create AI prompt
        prompt = self._create_classification_prompt(context)

        # Get AI response
        ai_response = self.gemini_ai.generate_response(
            prompt, "<EMAIL>")

        # Parse AI response
        result = self._parse_ai_response(ai_response, user_input)

        return result

    def _prepare_context(self, user_input: str, conversation_history: List[str] = None) -> Dict:
        """Prepare context for AI classification"""
        return {
            "current_input": user_input,
            "history": conversation_history or [],
            "intent_definitions": self.intent_definitions
        }

    def _create_classification_prompt(self, context: Dict) -> str:
        """Create prompt for AI classification"""
        prompt = f"""
Bạn là chuyên gia phân tích intent của khách hàng trong lĩnh vực tuyển dụng.

NHIỆM VỤ: Phân loại khách hàng và xác định intent cụ thể.

3 LOẠI KHÁCH HÀNG:

1. CURIOUS (Tò mò):
   - Không có nhu cầu tìm việc cụ thể
   - Chỉ muốn tìm hiểu, khám phá
   - Câu hỏi chung chung về công ty, ngành nghề
   - Thái độ casual
   Examples: "Công ty bạn làm gì?", "Tôi muốn tìm hiểu về FOIS GROUP", "Ngành IT thế nào?"

2. JOB_SEEKER (Tìm việc):
   - Đang tích cực tìm việc
   - Sẵn sàng chia sẻ thông tin
   - Hỏi trực tiếp về cơ hội việc làm
   - Muốn hành động cụ thể
   Examples: "Tôi muốn tìm việc", "Có vị trí Python Developer không?", "Lương bao nhiêu?"

3. PRIVATE (Kín đáo):
   - Có quan tâm nhưng thận trọng
   - Không muốn chia sẻ thông tin cá nhân
   - Hỏi gián tiếp, ẩn danh
   - Giữ khoảng cách
   Examples: "Bạn tôi hỏi về việc làm", "Nếu như có người...", "Thông tin chung về lương"

SPECIFIC INTENTS cho CURIOUS:
- company_info: Hỏi về công ty, FOIS GROUP chung chung
- company_culture: Hỏi về văn hóa công ty, môi trường làm việc
- career_development: Hỏi về cơ hội phát triển, thăng tiến
- technology: Hỏi về công nghệ, tech stack
- industry: Hỏi về ngành IT, xu hướng
- general_greeting: Chào hỏi đơn thuần
- casual_chat: Trò chuyện phiếm

INPUT CẦN PHÂN TÍCH:
"{context['current_input']}"

LỊCH SỬ HỘI THOẠI:
{context['history'][-3:] if context['history'] else "Không có"}

YÊU CẦU PHẢN HỒI:
Trả lời theo format JSON:
{{
    "customer_type": "curious/job_seeker/private",
    "specific_intent": "company_info/technology/industry/general_greeting/casual_chat/job_search/etc",
    "confidence": 0.85,
    "reasoning": "Lý do phân loại",
    "response_style": "Phong cách phản hồi phù hợp"
}}

PHÂN TÍCH:
"""
        return prompt

    def _parse_ai_response(self, ai_response: str, user_input: str) -> IntentResult:
        """Parse AI response into IntentResult"""
        try:
            import json
            import re

            # Extract JSON from AI response
            json_match = re.search(r'\{.*\}', ai_response, re.DOTALL)
            if json_match:
                result_data = json.loads(json_match.group())
            else:
                # Fallback parsing
                result_data = self._fallback_parsing(ai_response, user_input)

            # Map to enums
            customer_type_str = result_data.get(
                'customer_type', 'curious').lower()
            customer_type = CustomerType(customer_type_str)

            # Get specific intent based on customer type
            specific_intent = self._get_specific_intent(
                customer_type,
                result_data.get('specific_intent', ''),
                user_input
            )

            return IntentResult(
                customer_type=customer_type,
                specific_intent=specific_intent,
                confidence=float(result_data.get('confidence', 0.7)),
                reasoning=result_data.get('reasoning', 'AI classification'),
                suggested_response_style=result_data.get(
                    'response_style', 'friendly')
            )

        except Exception as e:
            # Fallback to rule-based classification
            return self._fallback_classification(user_input)

    def _get_specific_intent(self, customer_type: CustomerType, intent_name: str, user_input: str) -> Enum:
        """Get specific intent enum based on customer type"""

        # Try to map AI intent name first
        if intent_name:
            mapped_intent = self._map_ai_intent_to_enum(
                customer_type, intent_name)
            if mapped_intent:
                return mapped_intent

        # Fallback to rule-based classification
        if customer_type == CustomerType.CURIOUS:
            return self._classify_curious_intent(user_input)
        elif customer_type == CustomerType.JOB_SEEKER:
            return self._classify_job_seeker_intent(user_input)
        else:  # PRIVATE
            return self._classify_private_intent(user_input)

    def _map_ai_intent_to_enum(self, customer_type: CustomerType, intent_name: str) -> Optional[Enum]:
        """Map AI intent name to specific enum"""
        intent_lower = intent_name.lower().strip()

        if customer_type == CustomerType.CURIOUS:
            intent_mapping = {
                'company_info': CuriousIntent.COMPANY_INFO,
                'company_culture': CuriousIntent.COMPANY_CULTURE,
                'culture': CuriousIntent.COMPANY_CULTURE,
                'career_development': CuriousIntent.CAREER_DEVELOPMENT,
                'career': CuriousIntent.CAREER_DEVELOPMENT,
                'development': CuriousIntent.CAREER_DEVELOPMENT,
                'technology': CuriousIntent.TECHNOLOGY_INTEREST,
                'industry': CuriousIntent.INDUSTRY_QUESTIONS,
                'general_greeting': CuriousIntent.GENERAL_GREETING,
                'casual_chat': CuriousIntent.CASUAL_CHAT,
                'comparison': CuriousIntent.COMPARISON_QUESTIONS,
                'news': CuriousIntent.NEWS_UPDATES,
                'goodbye': CuriousIntent.GOODBYE,
                'browsing': CuriousIntent.WEBSITE_BROWSING
            }
            return intent_mapping.get(intent_lower)

        elif customer_type == CustomerType.JOB_SEEKER:
            intent_mapping = {
                'job_search': JobSeekerIntent.JOB_SEARCH,
                'salary': JobSeekerIntent.SALARY_INQUIRY,
                'cv': JobSeekerIntent.CV_SUBMISSION,
                'interview': JobSeekerIntent.INTERVIEW_PREP,
                'skills': JobSeekerIntent.SKILL_REQUIREMENTS,
                'advice': JobSeekerIntent.CAREER_ADVICE,
                'status': JobSeekerIntent.APPLICATION_STATUS,
                'environment': JobSeekerIntent.WORK_ENVIRONMENT,
                'benefits': JobSeekerIntent.BENEFITS_INQUIRY,
                'urgent': JobSeekerIntent.URGENT_HIRING
            }
            return intent_mapping.get(intent_lower)

        elif customer_type == CustomerType.PRIVATE:
            intent_mapping = {
                'anonymous': PrivateIntent.ANONYMOUS_INQUIRY,
                'opportunities': PrivateIntent.GENERAL_OPPORTUNITIES,
                'research': PrivateIntent.MARKET_RESEARCH,
                'indirect': PrivateIntent.INDIRECT_QUESTIONS,
                'hypothetical': PrivateIntent.HYPOTHETICAL_SCENARIOS,
                'friend': PrivateIntent.FRIEND_INQUIRY,
                'planning': PrivateIntent.FUTURE_PLANNING,
                'trends': PrivateIntent.INDUSTRY_TRENDS,
                'skill_dev': PrivateIntent.SKILL_DEVELOPMENT,
                'exploration': PrivateIntent.CAUTIOUS_EXPLORATION
            }
            return intent_mapping.get(intent_lower)

        return None

    def _classify_curious_intent(self, user_input: str) -> CuriousIntent:
        """Classify curious customer intent"""
        user_lower = user_input.lower()

        # Check for specific intents first (more specific)
        if any(word in user_lower for word in ['văn hóa', 'culture', 'môi trường làm việc', 'work environment']):
            return CuriousIntent.COMPANY_CULTURE
        elif any(word in user_lower for word in ['cơ hội', 'phát triển', 'career', 'development', 'thăng tiến']):
            return CuriousIntent.CAREER_DEVELOPMENT
        elif any(word in user_lower for word in ['công ty', 'company', 'doanh nghiệp', 'fois group', 'fois']):
            return CuriousIntent.COMPANY_INFO
        elif any(word in user_lower for word in ['tìm hiểu', 'tò mò', 'curious']) and any(word in user_lower for word in ['về', 'about']):
            return CuriousIntent.COMPANY_INFO
        elif any(word in user_lower for word in ['chào', 'hello', 'hi', 'xin chào']) and 'tìm hiểu' not in user_lower:
            return CuriousIntent.GENERAL_GREETING
        elif any(word in user_lower for word in ['công nghệ', 'technology', 'tech']):
            return CuriousIntent.TECHNOLOGY_INTEREST
        elif any(word in user_lower for word in ['ngành', 'industry', 'lĩnh vực']):
            return CuriousIntent.INDUSTRY_QUESTIONS
        elif any(word in user_lower for word in ['so sánh', 'compare', 'khác']):
            return CuriousIntent.COMPARISON_QUESTIONS
        elif any(word in user_lower for word in ['tin tức', 'news', 'cập nhật']):
            return CuriousIntent.NEWS_UPDATES
        elif any(word in user_lower for word in ['tạm biệt', 'bye', 'goodbye']):
            return CuriousIntent.GOODBYE
        elif any(word in user_lower for word in ['tò mò', 'curious', 'tìm hiểu']):
            return CuriousIntent.WEBSITE_BROWSING
        else:
            return CuriousIntent.CASUAL_CHAT

    def _classify_job_seeker_intent(self, user_input: str) -> JobSeekerIntent:
        """Classify job seeker intent"""
        user_lower = user_input.lower()

        if any(word in user_lower for word in ['tìm việc', 'job', 'việc làm', 'tuyển dụng']):
            return JobSeekerIntent.JOB_SEARCH
        elif any(word in user_lower for word in ['lương', 'salary', 'tiền']):
            return JobSeekerIntent.SALARY_INQUIRY
        elif any(word in user_lower for word in ['cv', 'resume', 'hồ sơ']):
            return JobSeekerIntent.CV_SUBMISSION
        elif any(word in user_lower for word in ['phỏng vấn', 'interview']):
            return JobSeekerIntent.INTERVIEW_PREP
        elif any(word in user_lower for word in ['skill', 'kỹ năng', 'yêu cầu']):
            return JobSeekerIntent.SKILL_REQUIREMENTS
        elif any(word in user_lower for word in ['tư vấn', 'advice', 'hướng dẫn']):
            return JobSeekerIntent.CAREER_ADVICE
        elif any(word in user_lower for word in ['trạng thái', 'status', 'kết quả']):
            return JobSeekerIntent.APPLICATION_STATUS
        elif any(word in user_lower for word in ['môi trường', 'environment', 'văn hóa']):
            return JobSeekerIntent.WORK_ENVIRONMENT
        elif any(word in user_lower for word in ['phúc lợi', 'benefits', 'quyền lợi']):
            return JobSeekerIntent.BENEFITS_INQUIRY
        elif any(word in user_lower for word in ['gấp', 'urgent', 'nhanh']):
            return JobSeekerIntent.URGENT_HIRING
        else:
            return JobSeekerIntent.JOB_SEARCH

    def _classify_private_intent(self, user_input: str) -> PrivateIntent:
        """Classify private customer intent"""
        user_lower = user_input.lower()

        if any(word in user_lower for word in ['bạn tôi', 'người quen', 'ai đó']):
            return PrivateIntent.FRIEND_INQUIRY
        elif any(word in user_lower for word in ['nếu như', 'giả sử', 'hypothetical']):
            return PrivateIntent.HYPOTHETICAL_SCENARIOS
        elif any(word in user_lower for word in ['nghiên cứu', 'research', 'tìm hiểu']):
            return PrivateIntent.MARKET_RESEARCH
        elif any(word in user_lower for word in ['xu hướng', 'trends', 'tương lai']):
            return PrivateIntent.INDUSTRY_TRENDS
        elif any(word in user_lower for word in ['phát triển', 'development', 'học']):
            return PrivateIntent.SKILL_DEVELOPMENT
        elif any(word in user_lower for word in ['kế hoạch', 'planning', 'dự định']):
            return PrivateIntent.FUTURE_PLANNING
        elif any(word in user_lower for word in ['cơ hội', 'opportunities', 'khả năng']):
            return PrivateIntent.GENERAL_OPPORTUNITIES
        elif any(word in user_lower for word in ['ẩn danh', 'anonymous', 'không nói tên']):
            return PrivateIntent.ANONYMOUS_INQUIRY
        elif any(word in user_lower for word in ['thận trọng', 'careful', 'cẩn thận']):
            return PrivateIntent.CAUTIOUS_EXPLORATION
        else:
            return PrivateIntent.INDIRECT_QUESTIONS

    def _fallback_classification(self, user_input: str) -> IntentResult:
        """Fallback rule-based classification"""
        user_lower = user_input.lower()

        # Simple keyword-based classification
        if any(word in user_lower for word in ['tìm việc', 'ứng tuyển', 'cv', 'lương']):
            return IntentResult(
                customer_type=CustomerType.JOB_SEEKER,
                specific_intent=JobSeekerIntent.JOB_SEARCH,
                confidence=0.6,
                reasoning="Fallback: Job-related keywords detected",
                suggested_response_style="helpful_direct"
            )
        elif any(word in user_lower for word in ['bạn tôi', 'ai đó', 'nếu như']):
            return IntentResult(
                customer_type=CustomerType.PRIVATE,
                specific_intent=PrivateIntent.INDIRECT_QUESTIONS,
                confidence=0.6,
                reasoning="Fallback: Indirect language detected",
                suggested_response_style="gentle_informative"
            )
        else:
            return IntentResult(
                customer_type=CustomerType.CURIOUS,
                specific_intent=CuriousIntent.CASUAL_CHAT,
                confidence=0.5,
                reasoning="Fallback: Default to curious",
                suggested_response_style="friendly_casual"
            )

    def _fallback_parsing(self, ai_response: str, user_input: str) -> Dict:
        """Fallback parsing when JSON extraction fails"""
        return {
            "customer_type": "curious",
            "specific_intent": "casual_chat",
            "confidence": 0.5,
            "reasoning": "Fallback parsing",
            "response_style": "friendly"
        }
