<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Token UI Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .token-toggle-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-bottom: 20px;
        }
        
        .token-toggle-btn:hover {
            background: #0056b3;
        }
        
        .token-toggle-btn.active {
            background: #28a745;
        }
        
        .token-usage-container {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-top: 10px;
        }
        
        .token-usage-container.hidden {
            display: none;
        }
        
        .token-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .token-stat {
            text-align: center;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border: 1px solid #e9ecef;
        }
        
        .token-label {
            font-size: 0.8rem;
            color: #6c757d;
            margin-bottom: 5px;
        }
        
        .token-value {
            font-size: 1.2rem;
            font-weight: bold;
            color: #007bff;
        }
        
        .test-message {
            background: #e3f2fd;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .simulate-btn {
            background: #17a2b8;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Token UI Test</h1>
        <p>This page tests the token usage display functionality.</p>
        
        <button class="token-toggle-btn" id="tokenToggle">
            📊 Toggle Token Display
        </button>
        
        <div class="token-usage-container hidden" id="tokenUsageContainer">
            <div class="token-usage-header">
                <h3>📊 Token Usage Statistics</h3>
            </div>
            <div class="token-stats">
                <div class="token-stat">
                    <div class="token-label">Total Tokens</div>
                    <div class="token-value" id="totalTokens">0</div>
                </div>
                <div class="token-stat">
                    <div class="token-label">Input Tokens</div>
                    <div class="token-value" id="inputTokens">0</div>
                </div>
                <div class="token-stat">
                    <div class="token-label">Output Tokens</div>
                    <div class="token-value" id="outputTokens">0</div>
                </div>
                <div class="token-stat">
                    <div class="token-label">Messages</div>
                    <div class="token-value" id="messageCount">0</div>
                </div>
            </div>
            <div class="token-details">
                <div>Last Message: <span id="lastMessageTokens">0 tokens</span></div>
                <div>Avg per Message: <span id="avgTokensPerMessage">0 tokens</span></div>
            </div>
        </div>
        
        <div class="test-message">
            <h3>🎮 Test Controls</h3>
            <button class="simulate-btn" onclick="simulateMessage(100, 50)">Simulate Message (100 in, 50 out)</button>
            <button class="simulate-btn" onclick="simulateMessage(200, 150)">Simulate Message (200 in, 150 out)</button>
            <button class="simulate-btn" onclick="simulateMessage(500, 300)">Simulate Large Message</button>
            <button class="simulate-btn" onclick="resetTokens()">Reset Tokens</button>
        </div>
        
        <div class="test-message">
            <h3>📝 Instructions</h3>
            <ol>
                <li>Click "Toggle Token Display" to show/hide the token stats</li>
                <li>Use the simulate buttons to test token updates</li>
                <li>Check browser console for debug logs</li>
                <li>Verify that numbers update correctly</li>
            </ol>
        </div>
    </div>

    <script>
        // Mock token tracking functionality
        let tokenStats = {
            totalTokens: 0,
            inputTokens: 0,
            outputTokens: 0,
            messageCount: 0,
            lastMessageTokens: 0
        };
        
        let tokenDisplayVisible = false;
        
        // Elements
        const tokenToggle = document.getElementById('tokenToggle');
        const tokenUsageContainer = document.getElementById('tokenUsageContainer');
        const totalTokensEl = document.getElementById('totalTokens');
        const inputTokensEl = document.getElementById('inputTokens');
        const outputTokensEl = document.getElementById('outputTokens');
        const messageCountEl = document.getElementById('messageCount');
        const lastMessageTokensEl = document.getElementById('lastMessageTokens');
        const avgTokensPerMessageEl = document.getElementById('avgTokensPerMessage');
        
        // Toggle functionality
        tokenToggle.addEventListener('click', () => {
            console.log('🔄 Toggling token display');
            tokenDisplayVisible = !tokenDisplayVisible;
            
            if (tokenDisplayVisible) {
                tokenUsageContainer.classList.remove('hidden');
                tokenToggle.classList.add('active');
                tokenToggle.textContent = '📊 Hide Token Display';
                updateTokenDisplay();
            } else {
                tokenUsageContainer.classList.add('hidden');
                tokenToggle.classList.remove('active');
                tokenToggle.textContent = '📊 Show Token Display';
            }
        });
        
        // Update display
        function updateTokenDisplay() {
            console.log('🖥️ Updating token display with:', tokenStats);
            
            const avgTokens = tokenStats.messageCount > 0 
                ? Math.round(tokenStats.totalTokens / tokenStats.messageCount)
                : 0;
            
            totalTokensEl.textContent = tokenStats.totalTokens.toLocaleString();
            inputTokensEl.textContent = tokenStats.inputTokens.toLocaleString();
            outputTokensEl.textContent = tokenStats.outputTokens.toLocaleString();
            messageCountEl.textContent = tokenStats.messageCount.toString();
            lastMessageTokensEl.textContent = `${tokenStats.lastMessageTokens} tokens`;
            avgTokensPerMessageEl.textContent = `${avgTokens} tokens`;
        }
        
        // Simulate message
        function simulateMessage(inputTokens, outputTokens) {
            console.log(`📨 Simulating message: ${inputTokens} in, ${outputTokens} out`);
            
            const totalMessageTokens = inputTokens + outputTokens;
            
            tokenStats.inputTokens += inputTokens;
            tokenStats.outputTokens += outputTokens;
            tokenStats.totalTokens += totalMessageTokens;
            tokenStats.messageCount += 1;
            tokenStats.lastMessageTokens = totalMessageTokens;
            
            if (tokenDisplayVisible) {
                updateTokenDisplay();
            }
        }
        
        // Reset tokens
        function resetTokens() {
            console.log('🔄 Resetting tokens');
            tokenStats = {
                totalTokens: 0,
                inputTokens: 0,
                outputTokens: 0,
                messageCount: 0,
                lastMessageTokens: 0
            };
            
            if (tokenDisplayVisible) {
                updateTokenDisplay();
            }
        }
        
        console.log('✅ Token UI test page loaded');
    </script>
</body>
</html>
